{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f8150932-3b17-4134-bab0-31f8eb98a7c4", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "pd.set_option('display.max_columns', None)\n", "\n", "sns.set(style=\"whitegrid\")\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 2, "id": "b097d337-dad5-4ddc-8a17-fabc80671cfb", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>contentTopicName</th>\n", "      <th>cmsDocumentId</th>\n", "      <th>fragmentId</th>\n", "      <th>probability</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>59</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>1</td>\n", "      <td>0.035310</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>59</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>2</td>\n", "      <td>0.035310</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>59</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>3</td>\n", "      <td>0.035310</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>59</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>4</td>\n", "      <td>0.033901</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>92</td>\n", "      <td>CGM vs. BGM</td>\n", "      <td>ADC-94563</td>\n", "      <td>1</td>\n", "      <td>0.292987</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92572</th>\n", "      <td>634255</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>4</td>\n", "      <td>0.024113</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92573</th>\n", "      <td>634270</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>1</td>\n", "      <td>0.139802</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92574</th>\n", "      <td>634270</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>2</td>\n", "      <td>0.139802</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92575</th>\n", "      <td>634270</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>3</td>\n", "      <td>0.139802</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92576</th>\n", "      <td>634270</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>4</td>\n", "      <td>0.146622</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>92577 rows × 5 columns</p>\n", "</div>"], "text/plain": ["       accountId                       contentTopicName cmsDocumentId  \\\n", "0             59  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "1             59  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "2             59  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "3             59  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "4             92                            CGM vs. BGM     ADC-94563   \n", "...          ...                                    ...           ...   \n", "92572     634255  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "92573     634270  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "92574     634270  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "92575     634270  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "92576     634270  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "\n", "       fragmentId  probability  \n", "0               1     0.035310  \n", "1               2     0.035310  \n", "2               3     0.035310  \n", "3               4     0.033901  \n", "4               1     0.292987  \n", "...           ...          ...  \n", "92572           4     0.024113  \n", "92573           1     0.139802  \n", "92574           2     0.139802  \n", "92575           3     0.139802  \n", "92576           4     0.146622  \n", "\n", "[92577 rows x 5 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_parquet(\"s3://aktana-bdp-abbottus/prod/content_selection/predictions/runmonth=2025-07/results_2025-07-24.parquet\")\n", "df"]}, {"cell_type": "code", "execution_count": 3, "id": "8fdd1cbb-05be-424d-b6fa-d3d2c4c51010", "metadata": {}, "outputs": [{"data": {"text/plain": ["22657"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df['accountId'].nunique()"]}, {"cell_type": "code", "execution_count": 4, "id": "1648cf3a-7081-4cd4-999f-1d33cff3fe37", "metadata": {}, "outputs": [{"data": {"text/plain": ["cmsDocumentId\n", "ADC-102760    16062\n", "ADC-102761        3\n", "ADC-94563      1405\n", "ADC-94575      4499\n", "ADC-94590       144\n", "ADC-96117       544\n", "Name: accountId, dtype: int64"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df.groupby('cmsDocumentId')['accountId'].nunique()"]}, {"cell_type": "markdown", "id": "9cce3b3b-7ba0-440b-9cb3-94cd544e64c2", "metadata": {}, "source": ["# Max Probability per (accountId, cmsDocumentId) and its Distribution"]}, {"cell_type": "code", "execution_count": 5, "id": "eb3a37b8-4f20-4cff-b72a-0ba7e0c0e4ea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   accountId cmsDocumentId  max_probability\n", "0         59    ADC-102760         0.035310\n", "1         92     ADC-94563         0.321760\n", "2        125     ADC-94575         0.053361\n", "3        133    ADC-102760         0.175619\n", "4        150     ADC-94575         0.077659\n", "5        193     ADC-94575         0.199455\n", "6        267     ADC-94575         0.090203\n", "7        328     ADC-94563         0.003456\n", "8        334     ADC-94563         0.598743\n", "9        371    ADC-102760         0.187887\n"]}], "source": ["max_prob_df = df.groupby(['accountId', 'cmsDocumentId'])['probability'].max().reset_index()\n", "max_prob_df.rename(columns={'probability': 'max_probability'}, inplace=True)\n", "\n", "print(max_prob_df.head(10))"]}, {"cell_type": "code", "execution_count": 6, "id": "8527b8f4-9eb6-4eda-8e07-404facaed3dc", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "sns.histplot(max_prob_df['max_probability'], bins=20, kde=True)\n", "plt.title('Distribution of Max Probability per (Account, CMS Document)')\n", "plt.xlabel('Max Probability')\n", "plt.ylabel('Frequency')\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "be3ae881-44ba-44a0-968f-9fd112035c2f", "metadata": {}, "source": ["# Difference Across Fragments per (accountId, cmsDocumentId)"]}, {"cell_type": "code", "execution_count": 7, "id": "bdba7fa9-eec8-4a98-b376-10bcef940b4b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   accountId cmsDocumentId       max       min  probability_range\n", "0        334     ADC-94563  0.598743  0.393992           0.204752\n", "1        452     ADC-94575  0.164371  0.101497           0.062874\n", "2       1390     ADC-94575  0.794551  0.701232           0.093319\n", "3       1393     ADC-94575  0.460968  0.356635           0.104333\n", "4       1506     ADC-94575  0.579588  0.500319           0.079268\n", "5       1534     ADC-94575  0.505400  0.450308           0.055092\n", "6       1645     ADC-94575  0.210262  0.160163           0.050099\n", "7       2000     ADC-94563  0.455946  0.387851           0.068095\n", "8       2029    ADC-102760  0.156952  0.104631           0.052321\n", "9       2498     ADC-94575  0.889945  0.823402           0.066543\n"]}], "source": ["# Group by accountId and cmsDocumentId\n", "agg_diff_df = df.groupby(['accountId', 'cmsDocumentId'])['probability'].agg(['max', 'min'])\n", "agg_diff_df['probability_range'] = agg_diff_df['max'] - agg_diff_df['min']\n", "agg_diff_df = agg_diff_df[agg_diff_df.probability_range > 0.05]\n", "agg_diff_df = agg_diff_df.reset_index()\n", "\n", "print(agg_diff_df.head(10))"]}, {"cell_type": "code", "execution_count": 8, "id": "0f048986-c0ce-49a8-b3c6-401be4cbb070", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "sns.histplot(agg_diff_df['probability_range'], bins=10, kde=True)\n", "plt.title('Distribution of Probability Range Across Fragments')\n", "plt.xlabel('Probability Range (Max - Min)')\n", "plt.ylabel('Frequency')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 9, "id": "9910e1b1-64a1-4a74-aeae-a70ada318f3c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "agg_diff_df = df.groupby(['accountId', 'cmsDocumentId'])['probability'].agg(['max', 'min'])\n", "agg_diff_df['probability_range'] = agg_diff_df['max'] - agg_diff_df['min']\n", "agg_diff_df = agg_diff_df[agg_diff_df.probability_range > 0.05]\n", "agg_diff_df = agg_diff_df.reset_index()\n", "\n", "unique_docs = agg_diff_df['cmsDocumentId'].unique()\n", "\n", "for doc_id in unique_docs:\n", "    doc_df = agg_diff_df[agg_diff_df['cmsDocumentId'] == doc_id]\n", "\n", "    if doc_df.empty:\n", "        continue  # Skip if no data for this document\n", "\n", "    plt.figure(figsize=(10, 6))\n", "    sns.histplot(doc_df['probability_range'], bins=10)\n", "    plt.title(f'Distribution of Probability Range for cmsDocumentId: {doc_id}')\n", "    plt.xlabel('Probability Range (Max - Min)')\n", "    plt.ylabel('Frequency')\n", "    plt.tight_layout()\n", "    plt.show()\n"]}, {"cell_type": "markdown", "id": "873af77c-0a82-4e1d-b4b7-577247dee87a", "metadata": {}, "source": ["# Differences Across Accounts for same (cmsDocumentId, fragmentId)"]}, {"cell_type": "code", "execution_count": 10, "id": "2d37990f-c2f0-4045-9ede-26c28c0d3a04", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   cmsDocumentId  fragmentId      mean       min       max       std     range\n", "0     ADC-102760           1  0.143180  0.000013  0.940808  0.173444  0.940795\n", "1     ADC-102760           2  0.143180  0.000013  0.940808  0.173444  0.940795\n", "2     ADC-102760           3  0.143180  0.000013  0.940808  0.173444  0.940795\n", "3     ADC-102760           4  0.145656  0.000021  0.957097  0.167816  0.957076\n", "4     ADC-102761           1  0.099151  0.029436  0.140196  0.060692  0.110761\n", "5     ADC-102761           2  0.099151  0.029436  0.140196  0.060692  0.110761\n", "6     ADC-102761           3  0.099151  0.029436  0.140196  0.060692  0.110761\n", "7     ADC-102761           4  0.124066  0.054881  0.183722  0.064947  0.128841\n", "8      ADC-94563           1  0.151357  0.000015  0.875111  0.176716  0.875096\n", "9      ADC-94563           2  0.151357  0.000015  0.875111  0.176716  0.875096\n", "10     ADC-94563           3  0.151357  0.000015  0.875111  0.176716  0.875096\n", "11     ADC-94563           4  0.168477  0.000044  0.878294  0.185984  0.878250\n", "12     ADC-94563           5  0.150750  0.000024  0.838277  0.173532  0.838253\n", "13     ADC-94575           1  0.226885  0.000005  0.963774  0.223405  0.963769\n", "14     ADC-94575           2  0.226885  0.000005  0.963774  0.223405  0.963769\n", "15     ADC-94575           3  0.205982  0.000004  0.961227  0.204365  0.961223\n", "16     ADC-94575           4  0.205982  0.000004  0.961227  0.204365  0.961223\n", "17     ADC-94590           1  0.135494  0.000170  0.833844  0.170805  0.833674\n", "18     ADC-94590           2  0.135494  0.000170  0.833844  0.170805  0.833674\n", "19     ADC-94590           3  0.132367  0.000135  0.822830  0.167064  0.822694\n", "20     ADC-94590           4  0.132367  0.000135  0.822830  0.167064  0.822694\n", "21     ADC-96117           1  0.180457  0.000274  0.845114  0.187978  0.844840\n", "22     ADC-96117           2  0.180457  0.000274  0.845114  0.187978  0.844840\n", "23     ADC-96117           3  0.180457  0.000274  0.845114  0.187978  0.844840\n", "24     ADC-96117           4  0.180457  0.000274  0.845114  0.187978  0.844840\n", "25     ADC-96117           5  0.180457  0.000274  0.845114  0.187978  0.844840\n"]}], "source": ["# Group by cmsDocumentId and fragmentId\n", "account_diff_df = df.groupby(['cmsDocumentId', 'fragmentId'])['probability'].agg(['mean', 'min', 'max', 'std'])\n", "account_diff_df['range'] = account_diff_df['max'] - account_diff_df['min']\n", "account_diff_df = account_diff_df.reset_index()\n", "\n", "print(account_diff_df)\n"]}, {"cell_type": "code", "execution_count": 11, "id": "90a1b0e8-b943-4e12-847f-4b50584521e2", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA1MAAAImCAYAAAC2INBaAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAABcnElEQVR4nO3dd3xOd//H8feVJSFmWmK0FSNBJMSsLTGqqkroMjpQSmorerditUXtFbvcthoV1KrdW+11K3r3VqH2TMyQdX5/+OW6XRIjJ6nrwuv5eORBzjnX93zOub65rvO+zvecy2IYhiEAAAAAQJo42bsAAAAAAHgaEaYAAAAAwATCFAAAAACYQJgCAAAAABMIUwAAAABgAmEKAAAAAEwgTAEAAACACYQpAAAAADCBMAU8g/gubjxL6M/A4+FvJX3YfzCDMIVnSsuWLeXn52fzU7JkSdWsWVP9+/fX1atXM2Q9ISEh6t27d7rbWbJkifz8/HTq1KkHLnPq1Cn5+flpyZIlkqSxY8fKz8/POr9ly5Zq2bKl9feFCxdqyJAh6aprx44dKfZj8r6sXr26evbsqYsXL6ZrHfY0cuRI+fn5aeDAgfYuJUPduHFDpUqVkr+//1P9/Nxr/fr16tWrV7rbmTFjhqpUqaLAwEBFRERkQGW2Uvt7ufdn2LBhGbq+3r17KyQkxPTj16xZo2bNmtlMS0pK0sKFC9W8eXNVrFhRZcqUUePGjTVr1izFxcVZl0t+TfLz89OCBQtSbf/69esKCAiQn5+fduzYYZ2ekJCgGTNmqHHjxipdurSCgoLUuHFjff/99zbrSE3ya9+9P4GBgXrttdc0ZMgQxcTEmN4fT7tr166pZ8+e2r17d5oe96DX+uSfdu3a/U0VO549e/aobdu21t+jo6NVs2ZNnTx50o5V4WngYu8CgIxWokQJ9e3b1/p7fHy8Dh06pBEjRujIkSOaN2+eLBaLHStMm9y5c2vBggV6+eWXU51/77ZK0oQJE1ShQoUMWXd4eLj8/f2tv9+8eVN79uzR5MmTFRUVpYULF2bIep6kpKQkLV26VL6+voqMjFSPHj3k4eFh77IyxIoVK5Q1a1YlJiZq0aJFat++vb1LSrcZM2aku40bN25oyJAhqlmzplq1aqUCBQqkv7BUNG3aVG+//Xaq8/LkyfO3rNOMy5cvq3///poyZYp1WmxsrD799FMdOHBA77//vtq0aSNXV1dt375d3333nbZs2aLx48fLzc3N+hgnJyetXr1a7777bop1/Pzzz6mGoz59+mjt2rVq27atSpYsqaSkJO3evVujRo3Snj17NH78+EfWnxzgDMPQrVu3dPDgQU2ZMkUbNmzQvHnzlCtXLjO75al25MgRRUZGqkmTJqYef/9rfbJs2bKlt7SnxsKFC/Xnn39af8+ZM6c++ugj/eMf/9DMmTOfquMGPFmEKTxzPD09Vbp0aZtp5cuX182bNzVmzBgdOHAgxXxH5ubm9tB6ixQp8retu0iRIinWXaVKFcXFxWnKlCk6evTo37r+v8O//vUvnTt3TiNGjFCLFi20YsWKBx4AP22WLFmiatWqydXVVQsXLlS7du3k5MQAhKtXryopKUm1a9dW+fLl/7b1eHt7PxWvLRMmTFBgYKDNwfOgQYO0d+9ezZo1y2YbqlatqmLFiql79+6aP3++PvjgA+u8MmXKaMeOHbpy5UqKAPPTTz+pePHiOnLkiHXamTNn9OOPP2rAgAF65513rNOrVaumXLly6dtvv9W///1vBQYGPrT+1F6TKleurGbNmmnEiBH6+uuv07I7oNRf6yE1a9ZMEyZM0M8//6y6devauxw4KN5l8dwoWbKkpLtv6NLd4XE9evRQp06dVLp0aX388ceS7g5PGTRokGrXrq2AgAA1aNBAixYtStFefHy8vv76a5UvX17lypVTr169dOXKFZtlFi5cqNDQUJUuXVqBgYF66623tGrVqhRt7d27V40aNVLJkiXVoEEDrVy50jrv/mF+97t3mF9ISIhOnz6tH3/8UX5+fjp06JACAgI0YsQIm8fExsaqbNmymjBhwuPuPhvJn1be+0ndunXr1KxZMwUFBalkyZKqV6+e5syZY52fPJxk27ZtatWqlUqVKqUqVapo6NChSkxMtC5348YNhYeHq1KlSgoKClLXrl01Y8YMm6GNyesLDQ1VQECAqlSpoq+//lq3bt16ZO2LFy+Wr6+vypYtq4oVKz5wmNLmzZv13nvvqXTp0qpatarCw8N17do1SXdDS4kSJbRw4UJVqVJFFSpU0NGjRyVJK1euVGhoqIKCglSlShWFh4fbDC+9ffu2+vXrp+rVq1v307Rp02zW/c9//lP16tVTQECAqlWrpn79+unGjRsP3a6jR4/qwIEDqlmzpho2bKjTp0/rl19+SbHcjRs3NHDgQFWrVk2lS5dWkyZNtGnTJut8wzA0Y8YMvf766woMDFSdOnU0bdo0m2sJtm7dqmbNmln3Yffu3XX27Fnr/PuHoibz8/PT2LFjJf2vX69atUqdOnVSUFCQKlSooK+++sr6PLZs2VI7d+7Uzp07bYaLpWX/LFmyxDoc7h//+IdNXY96rsaOHas6depo3LhxqlChgqpWrZohQ4VPnTqlnj17qmrVqvL391elSpXUs2dPRUdHW5d5nOcheftee+01BQQEqGHDhtq8efND133lyhUtWrRIDRo0sJm2ePFiNWnSJNUD6gYNGqhVq1Ypzq7VqVNHTk5O+vnnn22mR0dHa/v27XrjjTdspl+6dEmGYSgpKSnFOt58801169bN9JmQwMBA1a1bV0uXLlVsbKx1+qP6qiQdO3ZMn332mSpUqKDy5curXbt21jMUya9b9w5VlFIOrw4JCdG4ceP07bffqmLFigoKClL37t118+ZNTZ48WdWrV1fZsmXVsWNHm+dZuvs+8cYbb1iHpI8dO9bmNbF379766KOPtHjxYr322msqWbKk3nrrLW3ZssVaY3LI/eCDD2zqyigPes1LTEzU5MmT1aBBAwUGBqp06dJ67733tH37dpvHb9q0SaGhodZhmStWrFCdOnWsrwf3vj+0bNlSgYGBqlmzphYuXKgLFy7os88+U1BQkGrUqJHibHVMTIzCw8NVuXJlBQQE6J133tG2bdtslvHz89OcOXP05ZdfqkKFCgoKClLnzp116dIl6z7+8ccfdfr0aZv3Wzc3N7322muaNGlShu9TPDsIU3huREVFSZJeeukl67RVq1YpS5YsmjBhgtq0aaPbt2+rWbNmWr58udq0aaOIiAiVLVtWX375pSZOnGjT3qpVq3To0CENHjxYvXr10qZNm/TJJ59Y3wTnzJmj8PBw1a5dW5MmTdKwYcPk5uamHj166Ny5czZthYeH6/XXX1dERISKFi2qrl27at26dWnexnHjxunFF19UjRo1tGDBAhUtWlS1a9fW8uXLbQ7Cfv75Z926dUuNGjV6aHtJSUlKSEiw/sTExGjt2rWaNm2aAgMD5ePjI+nuG2VYWJj8/f0VERGhsWPH6qWXXtKAAQN04MABmzZ79OihsmXLauLEiWrQoIGmTp1qM1ywQ4cOWrVqlTp27KiRI0fq5s2bGj58uE0by5cvV1hYmAoVKqTx48frs88+07Jly9ShQ4eHXkAcExOjDRs2WLe7cePGOnjwoA4dOmSz3MaNG9WuXTt5eXlp1KhR6tGjh9atW6euXbtal0lMTNT333+vb775Rl988YUKFy6siIgIdevWTaVLl9aYMWMUFhamNWvWqGXLlrp9+7Yk6dtvv9WWLVvUq1cvTZs2TbVq1dJ3332nxYsXS7o7VG/o0KFq3ry5pk2bprCwMEVGRj7y+q7FixcrR44cCg4OVrly5fTKK69o3rx5NsskJiaqVatWWr58udq1a6eIiAgVKlRIYWFh1mstvvvuO3333XcKCQnRxIkT1bRpUw0bNkyTJ0+WJC1dulStWrVS3rx5NWLECH3xxRfat2+f3n33XV2+fPmhNaamb9++yp8/vyIiItS6dWstWrTIGvL79u2rEiVKqESJElqwYIH8/f3TvH9q1qypcePGSZLat29vDc+P81xJdz982bx5s0aOHKkvvvhC2bNnf+C23P/3cu9PstjYWH3wwQf6888/1bdvX02bNk0ffPCBfvrpJ40cOdK63KOeB0k6e/asJk+erM6dO2vs2LGyWCzq1KnTQ5+HtWvXKiEhQcHBwdZp27ZtSzHtfr169dJrr71mMy1btmyqUqWKVq9ebTN9zZo1ypcvX4ozTMWKFVPevHk1aNAg9e/fX1u2bLGG4Fy5cqldu3YqWLDgA2t4lCpVqig+Pl4HDx6U9Hh99fz583r33Xd1/Phx9evXT0OHDtWlS5f04YcfpvkarO+//15nz57VyJEj1b59e61YsUJNmjTRv/71Lw0cOFDdunXT+vXrNWbMGOtjJk2apD59+qhSpUqaOHGimjdvrilTpqhPnz42bf/222+aNm2aOnXqpPHjx8vZ2VkdO3bU1atX5e/vr/DwcEl330vuH/r9OFLru/cGOin117xhw4YpIiJC7777rqZOnaqBAwcqJiZGnTt3toba7du3q0OHDsqbN6/Gjh2r5s2bq2/fvilCrSR169ZNISEhmjRpknx8fNS3b1998MEHKlq0qCIiIhQYGKhBgwbp3//+tyTpzp07+vDDD7V+/Xp17dpV48aNk7e3t9q0aZMiUI0cOVJJSUkaMWKEevbsqY0bN+rbb7+VdPd9p0aNGnrxxRe1YMEC1axZ0/q4evXq6bfffrMeQwApGMAzpEWLFkbz5s2N+Ph468+lS5eMlStXGhUqVDDeffddIykpybpsqVKljDt37lgfP2fOHMPX19fYu3evTbv/+Mc/jICAACM6OtowDMMIDg42KleubNy8edO6zM8//2z4+voaGzZsMAzDMAYNGmQMHTrUpp3ffvvN8PX1NVasWGEYhmEsXrzY8PX1NaZOnWqzXKNGjYzGjRsbhmEYJ0+eNHx9fY3FixcbhmEYY8aMMXx9fW22uUWLFtbfg4ODjV69ell//+WXXwxfX19j27Zt1mkff/yx0apVqwfux+3btxu+vr6p/pQvX97o3bu3cfnyZevyU6ZMsVmnYRhGdHS04evra0yaNMmmzZEjR9osFxISYrRr184wDMP49ddfDV9fX2PNmjXW+YmJicbrr79u3eakpCSjevXqRuvWrW3aSX7sxo0bH7hdM2fONEqUKGFcvHjRMAzDuHXrllGmTBnjq6++slmucePGRqNGjax9xTAM46effjLq1q1rXLx40fq8LV261Do/JibGKFmypNGnTx+btnbt2mX4+voas2fPNgzDMF577bUU6xs3bpy17j59+hivvfaakZiYaJ0fGRlpzJw584HbFR8fb1SuXNkYMGCAdVpERIRRvHhx48yZM9ZpGzZsMHx9fY2ff/7ZOi0xMdF49913jbFjxxpXr141SpQoYXzzzTc27Q8cONBo3bq1kZiYaFSpUiVF3zlx4oTh7+9vDBkyxDCMlH00ma+vrzFmzBjDMP7Xr3v06GGzTMuWLY0GDRpYf7+/f5vZP/f/DT3uc5W8Hbt27Xpg2/du28N+kv9eDh8+bLz//vvGX3/9ZfP4du3aGa+99pphGMYjnwfDMIxevXoZvr6+xtGjR63zk/8G1q1b98A6O3fubDRs2NBm2pQpU1K09TD37s8ff/zRKF68uM3rQYsWLYwRI0ZY/+a3b99unfef//zHeOutt6z7pVixYkaTJk2MqVOnGrGxsQ9d74P6VbJNmzYZvr6+xk8//fTYfXXw4MFGYGCgceHCBesyZ8+eNWrWrGls2rQp1W1I3sb7X3erVatmxMfHW6fVq1fPCAoKMq5du2ad1q5dO+v+v3btmhEYGGiEh4fbtP3DDz8Yvr6+xh9//GEYxv+e6xMnTliX2blzp+Hr62usXr3aMAzjgXU+ysNe65P7o2EYqb7mGYZhdOvWzZgxY4bNtDVr1hi+vr7Gvn37DMMwjGbNmhkNGza0eT1dsWKFzetBch33vmfu37/f8PX1NT7//HPrtCtXrhi+vr7G9OnTDcMwjAULFhi+vr7G/v37rcskJSUZzZs3N0JDQ63TfH19jffff9+mzt69exulS5e2/t6rVy8jODg4xT66du2a4evra8yZMyf1nYjnHtdM4Zmza9euFBfSOjk5qXLlyhowYIDN0LRChQrZXFC9c+dO5c+fX0FBQTaPb9iwoRYtWqQDBw6oRo0akqQaNWooc+bM1mVCQkLk4uKiXbt2KTg42Hq3v2vXrunYsWM6ceKEdajI/Rdm169f3+b32rVra+zYsbp586bZ3WBVuXJl5cuXT5GRkXr11Vd17tw5bdu2TUOHDn3kY/v37y9/f38lJSVp/fr1mjp1qlq2bKmOHTvaLNemTRtJd29QERUVpb/++sv66fD923r/vvX29rYO69q+fbtcXV1Vu3Zt63wnJyfVr1/fOhzk2LFjOnfunNq1a2fziX/58uXl6emprVu32nyqeK/FixerYsWKcnNzsw7ZCwkJ0YoVK9SrVy95enrq9u3bOnz4sDp27GjTV+rXr5/ieSpevLj1//v371dcXJzN8ClJKleunPLnz6+dO3da75I2f/58nTt3TjVq1FCNGjUUFhZmXf7VV1/VggULFBoaqtq1a6tGjRp68803H3rx86ZNm3Tp0iXVrl3bZrtGjx6thQsXqlOnTpLu3q3K1dXV5i5wTk5Omj9/viRpy5YtSkhISHFtwFdffSVJ+vPPP3Xx4kV1797dZv7LL7+soKAg7dy584E1Psj9w8q8vb11+vTpBy5vZv/c73Gfq2T3Ps8P884779hcC3Sv5OFrxYsX19y5c5WUlKTjx4/rxIkTOnr0qI4dO2btz/v373/o85AsZ86cKly4sPX35BtrXL9+/YE1njx5MsUNOFxc7h4KpDb87lFq166tPn366Oeff9a7776rCxcuaPfu3QoPD08x7FmSfH19tXTpUh08eFD/+te/tGPHDu3bt08HDx7UokWLNGfOnAy5gURUVNRj9dU9e/aodOnSevHFF63LeHt7a+PGjZKUYnjfwwQGBlr3pSS98MILypw5s7JmzWqdliNHDv3xxx+SpH379un27dsKCQmxeS1L/vvcunWrihYtKunumbt7b0Lk7e0tSTZDGtMj+bX+Xu7u7imWu/9vIXnUwJUrV6zvc8n7Li4uTnFxcdq3b5/CwsJs/kbr1aunnj17pmj/3vcHLy8vSVKpUqWs03LmzCnpf31827ZtevHFF+Xv72+zD4ODg/Xdd9/p6tWr1rPJqb3WPM7+y5o1q7Jly/bQu+7i+UaYwjPH399f/fv3l3T3mp5MmTIpb9688vT0TLFslixZbH6/evWqzZtqshdeeEGSrAeqklIs5+TkpJw5c1qX+euvvxQeHq5t27bJ1dVVhQoVUrFixSSl/C6L5PaTeXl5yTCMR14n8zicnJwUGhqq6dOnq2/fvoqMjJSnp6fq1KnzyMf6+PgoICBA0t03NFdXV40bN06ZMmWyuYXslStX1LdvX61bt04Wi0WvvPKKypUrJynltt7/Bu3k5GRdJjo6Wjly5Ehx04TkN1VJ1qE3/fv3tz7P97pw4UKq23L48GHrxfCp3YRg2bJlatasma5evSrDMGzW+SD3hunka2nufy6TpyW/+X/55Zfy9vbWsmXLNHDgQA0cOFBBQUHq16+fihUrpvr16yspKUlz5861DpnMnz+/evTokSLMJUseIvjRRx+lmLdo0SJ16NBBLi4uiomJSXX/Jkvetw86mE2e/6BtPHz4cKqPe5j776R4b39IjZn9c7/Hfa6S3f868SC5c+e2/r08zPTp0zVx4kTFxMTohRdeUMmSJeXh4WFd76Oeh2T39j/pf9cwPiwU3bhxI8U+z5cvn6S7QxqTD97vd+HCBeXKlcsmLEh3b/hTvXp16139Vq9erSJFiqho0aIPDSIBAQEKCAhQ+/btFRsbq++//15jxozRlClTTN8KP3n4tLe392P31ZiYmAy7u2Nq7zH3P0f3Sq7x3tfSe937Wnb/c/Y4z3Va3Pta/zD3b8/BgwfVv39/HTx4UB4eHipSpIi1PxmGoZiYGCUmJqZ4PXV2dlaOHDlStJ/aPnzY3VZjYmJ08eLFVO9EKEkXL160hqm0vtbcX0NGvB/j2USYwjMnS5Ysj/WmkJrs2bPrxIkTKaYnf2dP8qdiklKMp09MTFR0dLS8vLyUlJSktm3bytXVVYsWLVLx4sXl4uKio0ePKjIyMkX7V69etXnTv3TpkpydnZU9e3brBbLpERoaqvHjx2vLli1atWqV6tevr0yZMqW5nfbt22vdunUaM2aMatasKV9fX0l3r4M6duyYZsyYoaCgILm5uSk2NlY//PBDmtrPkyePoqOjlZSUZHPAf+81IMmf8Pfs2TPVW8A/6JqWJUuWKHPmzIqIiEgRJsLDw7VgwQI1a9ZMnp6eslgsKT5Vv3PnjrZv327zKWlq67106ZIKFSpkM+/ixYvWa/Xc3NzUvn17tW/fXmfOnNHGjRsVERGh7t2766effpJ094L/Bg0a6Pr16/rXv/6lKVOm6PPPP1fZsmVT3ATg0qVL2rJli5o1a6Z69erZzNu/f79GjBihjRs3qk6dOsqaNatiYmJkGIbNp8SHDx+WYRjWfXvlyhWbbThz5oz++usva/9PrU9evHjROj+57cTERDk7O0tShpxlTZaW/ZOax32u/g7Lly/X4MGD9fnnnys0NNQamDp37mw9m/uo56Fs2bKm158zZ84UYfHVV1+Vq6urNm/ebD3zfr9PPvlEklJ9/apfv74+//xzXblyRStXrkxx44lkQ4YM0caNG1NcY+Xh4aGwsDCtXbvWeiMXM3799VdlzpxZ/v7+1rMIj+qrWbNmTfUM2rZt21SgQIEHhpabN28+dsh+kOTnediwYaleK5ZaEHQkN27cUJs2beTn56effvpJhQoVkpOTkzZv3qw1a9ZIuvtBmKura4rnISkpKUO+Fyxr1qwqWLDgA7/HLaOC8rVr12ze/4F7cQMK4B7ly5fX6dOntW/fPpvpy5Ytk6urq80F1Vu3brUZVrBmzRolJCSoYsWKio6OVlRUlJo2baqAgADrp7nJd1+6/4353rupJSUlafXq1SpVqlSqwyweJbWzDvnz51elSpU0c+ZMHTlyRKGhoWluV7o7HKhfv35KSEiwuf3wnj17VLduXesQOunB2/owFSpUUEJCgjZs2GCdZhiGzc04ChUqJC8vL506dcr66XZAQIDy5Mmj4cOHp3p2JC4uTsuXL1dISIgqVaqkihUr2vw0atRIv//+u/bv368sWbKoePHi1qEqybZs2aK2bds+8MxXqVKl5ObmphUrVthM3717t86cOaMyZcro9u3beu211/T9999LuntGoHnz5nrjjTesd5ns0qWLddhf1qxZ9frrr6tDhw5KSEhIdd2RkZFKSEjQhx9+mGK7PvzwQ3l6elqH8ZUrV07x8fHW5yZ5/37xxReaNGmSAgMD5erqmmLbv//+e3Xr1k1FixbViy++mGIbT548qf3796tMmTKS/vfp8r03WtmzZ0+q++1R7u/Pad0/qXmc5+rvsmfPHmXLlk1t2rSxBqnk729L/lt51POQHFDNyJcvX4oL/7Nly6amTZvqhx9+0G+//ZbiMUuXLtXvv/+uhg0bptpmcHCw3NzcNHv2bO3fv/+BYcrHx0dRUVE2dytNdvPmTV24cMH6AU1aHTlyROvXr1eTJk2UKVMm+fj4PFZfLVeunA4cOGATqC5fvqw2bdpo8+bNqfblq1ev2nwfkVnJZ/vPnz9v81rm4uKiESNGpGlYWXr6hFnHjh1TTEyMPvjgAxUpUsT6t3rva7+zs7PKlCmj9evX2zx2w4YNNu+fZlWoUEFnz56Vl5eXzT7cunWrpk6dmqb98qAz9levXlVsbKz1jBtwP85MAfcIDQ3V3LlzFRYWpk6dOqlAgQLasGGDFi9erM8++8zmtr0XL15Ux44d1bJlSx0/flwjRoxQlSpVVKlSJVksFuXPn19z5syRt7e3smXLpl9++UUzZ86UlHKc+6hRo5SYmKi8efNq3rx5ioqK0vTp001tQ7Zs2XT48GHt3LlTgYGB1kDWtGlTdevWTYULF37g2ZXHERQUpIYNGyoyMlKrVq2y3rp5+fLl8vf3l7e3t/bu3avJkyfLYrGkaUx/+fLlVaVKFX355Ze6dOmS8uXLp0WLFuk///mP9RNiZ2dnde3aVeHh4XJ2dlZwcLCuXbumiIgInT9/PtXhHuvWrVNMTEyKa2SSvfXWWxo9erTmz5+v0qVLq1OnTmrfvr26deumRo0a6dKlSxoxYoRq164tX1/fVA84c+TIobZt22r8+PFydXVVcHCwTp06pdGjR6tIkSJq3Lix3N3d5e/vr3HjxsnV1VV+fn6KiorSjz/+aL1T2quvvqq+fftqyJAhql69uq5du6Zx48apYMGC1mGi91qyZIn8/f1T/WTb3d1dr732mpYsWaKTJ0+qZs2aCgoKUu/evdWlSxe99NJLioyM1J9//qmBAwcqV65c+uCDDzRjxgy5ubmpQoUKOnDggObNm6eePXvKyclJ3bp10xdffKHu3burYcOGio6O1rhx45Q9e3br1wvUqFFDgwYNUnh4uFq3bq2zZ89q/Pjxpj7Jz5Ytm/bt26dt27apRIkSad4/qXmc58qMc+fOaf/+/anO8/DwkJ+fnwIDAzVv3jwNHjxYwcHBunDhgqZNm6ZLly5Zz5g9zvNgVpUqVbRq1Spdv37d5lqebt266eDBg2rZsqVatGhh/WBjy5Yt+uGHHxQcHKwPP/ww1TYzZ86sGjVqaPLkyQoMDHzgmb1GjRpp+fLl6tmzp3bs2KEaNWooW7ZsOn78uGbOnCl3d3e1atXqkduQvI8Nw9DNmzd18OBBzZgxQwULFlTnzp0l6bH76kcffaSlS5eqTZs2ateunVxdXTVhwgR5e3vrzTfflKenp/Lmzavx48dbz1pPmjQpQ77oO2fOnGrTpo1Gjx6tGzduqGLFijp//rxGjx4ti8Xy2P1ZkvW53LRpk7Jnz65ixYrpypUr+uuvv1SkSJFUh8+ll4+Pjzw9PTVx4kS5uLjIxcVFa9assX6VSPJrf6dOndSyZUt16tRJTZs21ZkzZzR69GhJSvcX4YaGhmr27Nn6+OOP9emnnypv3rz69ddfNWXKFLVo0UKurq6P3Va2bNl06dIlbd68WcWLF1fu3Lkl/e+DoKpVq6arVjy7CFPAPTw8PDRr1iwNHz7c+gZXqFAhffPNN2ratKnNss2aNdP169cVFhYmNzc3vfnmm/r888+tbw4RERH65ptv1Lt3b7m5ualIkSKaMGGCvv32W+3evdvmu0AGDRqkwYMH68SJE/L19dWUKVNSHcL2OFq1aqVvv/1WrVu31vTp063XLtWoUUMWi8X0Wal7Jd8q/LvvvlPNmjU1ePBg6/U/klSwYEH1799fy5Yts95y+3GNHDlSgwcP1vDhw5WQkKBatWrp/fff19KlS63LvP3228qSJYumTp2qBQsWKHPmzCpTpoyGDRuW6oHckiVLlD179ge+GebLl0/ly5fXqlWr9MUXXyg4OFgTJ07UuHHjFBYWply5cunNN99MceON+3Xs2FEvvPCCZs+erQULFihHjhyqV6+eunTpYr3WYMCAARo1apS+//57Xbx4UV5eXmratKn1IPC9995TfHy85s+fr7lz58rd3V2VKlXS559/nuLA4MCBAzp69GiqF3Ina9SokRYvXqwFCxaoR48emjJlioYNG6bRo0crNjZWfn5++v77761nXT///HN5eXlp/vz5mjp1qgoUKKA+ffrovffek3T34CVLliyaNGmSwsLC5OnpqWrVqqlbt27W6wh9fHw0ZMgQTZgwQW3btlXhwoVt+kdaNG/eXL/99ps++eQTDRo0KE3752Ee57lKq0WLFqX6nXTS3duCR0ZGqnHjxjp16pQWL16suXPnKk+ePKpRo4aaNWumPn366M8//1ThwoUf+TyYFRwcLBcXF/3yyy8215hly5ZNs2bN0uzZs7Vy5UrNmzdPhmGoYMGC+uqrr9S0adMU10vdq379+lq9evVDr1tzc3PTtGnTNHPmTK1evVo//fSTbt++rdy5cyskJETt27d/rGsV3333Xev/3d3d9dJLL+n9999XmzZtbELD4/TVvHnzau7cuRo6dKj1tbpixYoaOXKkNdyOGTNG3377rbp166YXXnhBH374oY4dO5Yht8ru0qWLXnzxRc2dO1dTp05V9uzZValSJXXr1s0m7D5K0aJF1aBBA82ZM0e//PKLVqxYoU2bNumLL77QzJkzVbFixXTXer+sWbMqIiJC3333nTp37mw9qz979mx98skn2r17t0JCQlSuXDmNHTtWo0ePVocOHZQ/f3716dNHXbt2TfdQycyZM2vOnDkaPny4hg4dquvXryt//vzq3r37YwXze4WGhmrz5s3WD1OTr2XbsmWLAgMDlT9//nTVimeXxXjcq+8APNVWrlypnj17avPmzY91wGIPp0+f1v79+1WrVi2bIY6dOnXSyZMn9eOPP9qxOuDZMHDgQP33v/+1ninHs6tnz55q1qxZql/G/KSsX79e3t7eNqMG/vvf/6pBgwaKiIhQrVq17Fbbo9y6dUvVqlXTkCFDbO4yC9yLM1PAM27dunU6ePCg5s+fr9DQUIcNUtLdoTm9e/dWrVq11LRpUzk7O+uXX37R2rVrNWjQIHuXBzwTPv30U73xxhv697//neKLdfHsOHr0qA4cOKB+/frZtY5//etfWrlypXr06CEfHx+dP39eEyZMUKFChRx+6Nz8+fNVtGhRhw58sD/OTAHPuBkzZmjUqFEqW7asRo0alaahI/awfft2jR8/XkeOHFFCQoIKFy6sjz/++IHXOwFIu5UrV2rmzJnWm5Pg2XPlyhXduXNHefPmtWsdt2/f1ujRo7VmzRpduHBBOXLkULVq1dS9e3eHvmPhlStX1KhRI82aNUuvvPKKvcuBAyNMAQAAAIAJ3BodAAAAAEwgTAEAAACACYQpAAAAADDhubub3759+2QYRpq+jwQAAADAsyc+Pl4Wi0VBQUGmHv/cnZkyDEPcc+PpYhiG4uLieN7gMOiTcDT0STga+iQczYP6ZHqzwXN3Zir5jFRAQICdK8HjunXrlo4cOaIiRYooc+bM9i4HoE/C4dAn4Wjok3A0D+qTBw8eTFe7z92ZKQAAAADICIQpAAAAADCBMAUAAAAAJhCmAAAAAMAEwhQAAAAAmECYAgAAAAATCFMAAAAAYAJhCgAAAABMIEwBAAAAgAmEKQAAAAAwgTAFAAAAACYQpgAAAADABMIUAAAAAJhAmAIAAAAAEwhTAAAAAGCCQ4WpSZMmqWXLljbTjhw5ohYtWqh06dIKCQnRzJkz7VQdAAAAAPyPw4SpOXPmaNSoUTbToqOj9fHHH+vll1/W4sWLFRYWpmHDhmnx4sX2KRIAAAAA/p+LvQs4f/68+vbtqx07dqhgwYI283744Qe5urpqwIABcnFxUeHChXXixAlNnjxZTZo0sU/BAAAAACAHODN16NAhubq6atmyZSpVqpTNvN27d6tChQpycflf5nv11Vd1/PhxXbp06UmXCgAAAABWdj8zFRISopCQkFTnnTt3Tr6+vjbTcufOLUk6e/asXnjhBVPrNAxDt27dMvVYPHmxsbE2/wL2Rp+Eo6FPwtHYs09aLJYnvs6HMQzD3iVAD+6ThmGkq8/YPUw9zO3bt+Xm5mYzLVOmTJKkO3fumG43Pj5eR44cSVdtePKOHz9u7xIAG/RJOBr6JBzNk+6Trq6uKuHvLxdn5ye63gdJSEzU4UOHFB8fb+9S8P9S65P35420cOgw5e7urri4OJtpySEqc+bMptt1dXVVkSJF0lUbnpzY2FgdP35cBQsWlIeHh73LAeiTcDj0STgae/VJi8UiF2dnzVv7uy5E23cUUu6cmfV+3WIqWrQoZ6ccwIP65NGjR9PVrkOHKW9vb124cMFmWvLvefLkMd2uxWJJVxiDfXh4ePC8waHQJ+Fo6JNwNPbqk5djbuv8ZfsOe3W23L01AR9wOJb7+2R6h4Xa/QYUD1O+fHnt2bNHiYmJ1mnbt2+Xj4+PvLy87FgZAAAAgOedQ4epJk2a6MaNG/ryyy919OhRLVmyRDNmzFC7du3sXRoAAACA55xDhykvLy9NnTpVUVFRaty4scaNG6eePXuqcePG9i4NAAAAwHPOoa6ZGjx4cIppgYGBWrBggR2qAQAAAIAHc+gzUwAAAADgqAhTAAAAAGACYQoAAAAATCBMAQAAAIAJhCkAAAAAMIEwBQAAAAAmEKYAAAAAwATCFAAAAACYQJgCAAAAABMIUwAAAABgAmEKAAAAAEwgTAEAAACACYQpAAAAADCBMAUAAAAAJhCmAAAAAMAEwhQAAAAAmECYAgAAAAATCFMAAAAAYAJhCgAAAABMIEwBAAAAgAmEKQAAAAAwgTAFAAAAACYQpgAAAADABMIUAAAAAJhAmAIAAAAAEwhTAAAAAGACYQoAAAAATCBMAQAAAIAJhCkAAAAAMIEwBQAAAAAmEKYAAAAAwATCFAAAAACYQJgCAAAAABMIUwAAAABgAmEKAAAAAEwgTAEAAACACYQpAAAAADCBMAUAAAAAJhCmAAAAAMAEwhQAAAAAmECYAgAAAAATCFMAAAAAYAJhCgAAAABMIEwBAAAAgAmEKQAAAAAwgTAFAAAAACYQpgAAAADABMIUAAAAAJhAmAIAAAAAEwhTAAAAAGACYQoAAAAATCBMAQAAAIAJhCkAAAAAMIEwBQAAAAAmEKYAAAAAwATCFAAAAACYQJgCAAAAABMIUwAAAABgAmEKAAAAAEwgTAEAAACACYQpAAAAADCBMAUAAAAAJhCmAAAAAMAEwhQAAAAAmECYAgAAAAATCFMAAAAAYAJhCgAAAABMIEwBAAAAgAmEKQAAAAAwgTAFAAAAACYQpgAAAADABMIUAAAAAJhAmAIAAAAAEwhTAAAAAGACYQoAAAAATCBMAQAAAIAJT0WYSkhI0OjRoxUcHKygoCA1b95c+/fvt3dZAAAAAJ5jT0WYmjBhghYuXKiBAwdq6dKl8vHxUZs2bXThwgV7lwYAAADgOfVUhKl169apQYMGqlq1ql555RX17t1b169f5+wUAAAAALt5KsKUl5eXNm7cqFOnTikxMVELFiyQm5ubihUrZu/SAAAAADynXOxdwOP48ssv1blzZ9WqVUvOzs5ycnLS2LFj9fLLL5tqzzAM3bp1K4OrxN8lNjbW5l/A3uiTcDT0STgae/VJi8UiDw8PJRpJSkxKfKLrvl+ikSTp7j4wDMOutUh3942jeZL75UF90jCMdO2bpyJMHT16VFmzZtX48eOVJ08eLVy4UD169NDs2bNVvHjxNLcXHx+vI0eO/A2V4u90/Phxe5cA2KBPwtHQJ+FonnSf9PDwUIkSJXQ79rZu3bTvB+e3Pe8eZkdFRdn9gw5XV1eV8PeXi7OzXeu4V0Jiog4fOqT4+Pgnut7U+qSbm5vp9hw+TJ09e1bdu3fXjBkzVK5cOUlSQECAjh49qrFjxyoiIiLNbbq6uqpIkSIZXSr+JrGxsTp+/LgKFiwoDw8Pe5cD0CfhcOiTcDT26pPJZxjcPdyVOUvCE1tvatw93CVJPj4+dj8zZbFY5OLsrHlrf9eFaPuPzsqdM7Per1tMRYsWfWL75kF98ujRo+lq1+HD1IEDBxQfH6+AgACb6aVKldKWLVtMtWmxWJQ5c+aMKA9PkIeHB88bHAp9Eo6GPglHY68+6WxxkrOTfc/COFvu3prAkT7guBxzW+cv2384sD33zf19Mr3DHx3+BhTe3t6SpP/85z820//44w8VLFjQDhUBAAAAwFMQpgIDA1W2bFn16tVL27dv1/HjxzVq1Cht27ZNbdu2tXd5AAAAAJ5TDj/Mz8nJSRMmTNCoUaP0xRdf6OrVq/L19dWMGTNUqlQpe5cHAAAA4Dnl8GFKkrJnz66+ffuqb9++9i4FAAAAACQ9BcP8AAAAAMAREaYAAAAAwATCFAAAAACYQJgCAAAAABMIUwAAAABgAmEKAAAAAEwgTAEAAACACYQpAAAAADCBMAUAAAAAJhCmAAAAAMAEwhQAAAAAmECYAgAAAAATCFMAAAAAYAJhCgAAAABMIEwBAAAAgAmEKQAAAAAwgTAFAAAAACYQpgAAAADABMIUAAAAAJhAmAIAAAAAEwhTAAAAAGACYQoAAAAATCBMAQAAAIAJhCkAAAAAMIEwBQAAAAAmEKYAAAAAwATCFAAAAACYQJgCAAAAABMIUwAAAABgAmEKAAAAAEwgTAEAAACACYQpAAAAADCBMAUAAAAAJhCmAAAAAMAEwhQAAAAAmECYAgAAAAATCFMAAAAAYAJhCgAAAABMIEwBAAAAgAmEKQAAAAAwgTAFAAAAACYQpgAAAADABMIUAAAAAJhAmAIAAAAAEwhTAAAAAGACYQoAAAAATCBMAQAAAIAJhCkAAAAAMIEwBQAAAAAmEKYAAAAAwATCFAAAAACYQJgCAAAAABMIUwAAAABgAmEKAAAAAEwgTAEAAACACYQpAAAAADCBMAUAAAAAJhCmAAAAAMAEwhQAAAAAmECYAgAAAAATCFMAAAAAYAJhCgAAAABMIEwBAAAAgAmEKQAAAAAwgTAFAAAAACYQpgAAAADABMIUAAAAAJhAmAIAAAAAEwhTAAAAAGACYQoAAAAATCBMAQAAAIAJhCkAAAAAMIEwBQAAAAAmEKYAAAAAwATCFAAAAACYQJgCAAAAABOemjC1dOlS1a9fXwEBAXrjjTe0atUqe5cEAAAA4Dn2VISpyMhIffnll2revLl++uknNWjQQN26ddO+ffvsXRoAAACA55SpMLVixQrFxcVldC2pMgxDo0eP1gcffKDmzZvr5ZdfVvv27VW5cmXt3LnzidQAAAAAAPdzMfOgnj17qn///nrjjTcUGhqqwMDAjK7LKioqSqdPn9abb75pM33atGl/2zoBAAAA4FFMhakNGzboxx9/VGRkpBYsWCAfHx81adJEDRs21IsvvpihBUZFRUmSbt26pdatW+vw4cMqUKCA2rdvr5CQEFNtGoahW7duZWSZ+BvFxsba/AvYG30SjoY+CUdjrz5psVjk4eGhRCNJiUmJT3Td90s0kiTd3QeGYdi1FkfaL5J99s2D+qRhGLJYLKbbtRjp3IK9e/dq6dKlWrt2rW7cuKGqVasqNDRUISEhcnExldVsREZGqmfPnipQoIA+++wzFStWTGvWrNHEiRM1ffp0VapUKU3tHTx48IkNUQQAAMCT4+HhoRIlSmjIjF916vw1u9ZSIE829fqosg4fPmz3Dzocab9IjrVvJMnNzU0BAQGmHpvutFOmTBmVKVNGb7/9tr777jtt2rRJmzZt0gsvvKAPP/xQrVq1krOzs+n2XV1dJUmtW7dW48aNJUnFixfX4cOHTYWp5DaLFCliuiY8WbGxsTp+/LgKFiwoDw8Pe5cD0CfhcOiTcDT26pPJZxjcPdyVOUvCE1tvatw93CVJPj4+DnFmSnKM/ZJch/Rk982D+uTRo0fT1W66wtTp06cVGRmpyMhI/fXXX3r55ZfVrVs31axZU5s2bdL48eN19OhRDRkyxPQ68uTJI0ny9fW1mV6kSBFt2rTJVJsWi0WZM2c2XRPsw8PDg+cNDoU+CUdDn4SjsVefdLY4ydnJ/If5GVWDJIf6gMMR9ktyHZJ99s39fTI9Q/wkk2Fq4cKFioyM1N69e5UpUybVq1dP33zzjcqVK2ddxtfXV9HR0Zo/f366wpS/v7+yZMmiAwcO2LT/xx9/6OWXXzbdLgAAAACkh6kw1adPH5UqVUr9+vVT/fr15enpmepyfn5+evfdd9NVoLu7u9q0aaPx48crT548CgwM1E8//aStW7dqxowZ6WobAAAAAMwyFaZWrFihIkWKKDEx0Xo91O3btxUfH6+sWbNal2vUqFGGFNmhQwd5eHho5MiROn/+vAoXLqyxY8eqYsWKGdI+AAAAAKSVqS/tLViwoPr27at33nnHOm3v3r2qVKmShgwZoqSkpAwrMNnHH3+s9evX67ffflNkZKRq166d4esAAAAAgMdlKkyNGTNGy5YtU4MGDazTSpQooR49euiHH37Q1KlTM6xAAAAAAHBEpob5LV++XL169dJ7771nnZYjRw599NFHcnFx0cyZM9W2bdsMKxIAAAAAHI2pM1PR0dF66aWXUp1XqFAhnTt3Ll1FAQAAAICjMxWmChUqpDVr1qQ6b8OGDXrllVfSVRQAAAAAODpTw/w++OAD9e7dWzExMapdu7a8vLx05coVbdy4UatWrdKgQYMyuk4AAAAAcCimwlSjRo108+ZNRUREaO3atdbpOXPmVJ8+fTLslugAAAAA4KhMhSlJat68uZo1a6aoqCjFxMQoW7ZsKlSokJycTI0cBAAAAICniukwJUkWi0WFChXKqFoAAAAA4KlhKkxduXJF33zzjTZt2qTY2FgZhmEz32Kx6PDhwxlSIAAAAAA4IlNhasCAAdq4caPeeOMNeXt7M7QPAAAAwHPHVJjasmWL/vGPf+jdd9/N6HoAAAAA4Klg6pSSq6vrA7+0FwAAAACeB6bCVJ06dbRixYqMrgUAAAAAnhqmhvmVKFFCo0aN0smTJ1WqVCm5u7vbzLdYLAoLC8uQAgEAAADAEZm+AYUk7dq1S7t27UoxnzAFAAAA4FlnKkz9/vvvGV0HAAAAADxV0n1P8+vXr+vPP/9UXFycEhMTM6ImAAAAAHB4psPUjh079Pbbb6tChQp688039d///lfdu3fX4MGDM7I+AAAAAHBIpsLUtm3b1Lp1a7m7u6tHjx4yDEOSVKxYMc2cOVPTp0/P0CIBAAAAwNGYClOjRo1SrVq1NGvWLH344YfWMPXpp5+qTZs2WrhwYYYWCQAAAACOxlSYOnLkiJo0aSLp7p377lWlShWdPn06/ZUBAAAAgAMzFaayZs2qixcvpjrv7Nmzypo1a7qKAgAAAABHZypM1apVSyNHjtTBgwet0ywWi86dO6eJEyeqZs2aGVUfAAAAADgkU98z1b17dx04cEDvvPOOXnjhBUlSt27ddO7cOeXNm1fdunXL0CIBAAAAwNGYClPZs2fXwoULtXTpUm3fvl0xMTHKmjWrWrZsqdDQUHl4eGR0nQAAAADgUEyFKUlyc3PTO++8o3feeScj6wEAAACAp4KpMLV06dJHLtOoUSMzTQMAAADAU8FUmOrdu3eq0y0Wi5ydneXs7EyYAgAAAPBMMxWm1q9fn2LarVu3tHv3bk2ZMkXjx49Pd2EAAAAA4MhMhan8+fOnOr1o0aKKj4/XwIEDNXfu3HQVBgAAAACOzNT3TD2Mn5+fDh06lNHNAgAAAIBDydAwFRcXp0WLFsnLyysjmwUAAAAAh2NqmF9ISIgsFovNtKSkJEVHR+vOnTvq1atXhhQHAAAAAI7KVJiqUKFCijAlSZ6engoODlblypXTXRgAAAAAODJTYWrw4MEZXQcAAAAAPFVMhakzZ86kafl8+fKZWQ0AAAAAOKwMu2bqYY4cOWJmNQAAAADgsEyFqVGjRqlv377y9/dXw4YNlSdPHkVHR2vDhg1atWqV2rdv/8DvogIAAACAZ4GpMBUZGang4OAU107Vr19fXl5e2rt3rz777LMMKRAAAAAAHJGp75natm2bGjRokOq86tWra8+ePekqCgAAAAAcnakwlTNnTh04cCDVedu2bVOePHnSVRQAAAAAODpTw/yaNm2qCRMmKDY2ViEhIcqVK5cuXbqk1atXa968eerTp09G1wkAAAAADsVUmOrQoYOuX7+uGTNmaNq0aZIkwzDk4eGhrl276r333svQIgEAAADA0ZgKUxaLRb1791aHDh20f/9+Xb16VTlz5lTp0qXl6emZ0TUCAAAAgMMxFaaSeXp6Knfu3JKk0qVLKyEhIUOKAgAAAABHZzpMRUZGavjw4bp48aIsFosWLlyosWPHytXVVcOHD5ebm1tG1gkAAAAADsXU3fxWrlypXr166dVXX9WIESOUlJQkSapTp442b96siIiIDC0SAAAAAByNqTNTEydO1Hvvvad+/fopMTHROr1Jkya6cuWKfvjhB3Xp0iWjagQAAAAAh2PqzFRUVJTq1KmT6rxSpUrp/Pnz6SoKAAAAABydqTDl5eWlP//8M9V5f/75p7y8vNJVFAAAAAA4OlNhqn79+hozZoxWr16tuLg4SXdvl/7bb78pIiJC9erVy9AiAQAAAMDRmLpmqkuXLvrjjz/UpUsXOTndzWMtW7bUrVu3VK5cOXXu3DlDiwQAAAAAR2MqTLm5uWnq1KnaunWrtm/frpiYGGXNmlUVKlRQjRo1ZLFYMrpOAAAAAHAopsJU69at1aZNG1WpUkVVqlTJ6JoAAAAAwOGZumZq7969nH0CAAAA8FwzFaaqVaumZcuWKT4+PqPrAQAAAICngqlhfpkyZdKyZcu0atUqFS5cWJkzZ7aZb7FY9M9//jNDCgQAAAAAR2QqTJ07d05BQUHW3w3DsJl//+8AAAAA8Kx57DC1du1avfrqq8qWLZtmzZr1d9YEAAAAAA7vsa+Z6ty5s44fP24zbcqUKbp8+XJG1wQAAAAADu+xw9T9Q/cSExM1YsQInTt3LsOLAgAAAABHZ+pufsm4NgoAAADA8ypdYQoAAAAAnleEKQAAAAAwId1hymKxZEQdAAAAAPBUSdP3TIWFhcnNzc1m2qeffipXV1ebaRaLRevWrUt/dQAAAADgoB47TDVu3PjvrAMAAAAAniqPHaYGDRr0d9YBAAAAAE8VbkABAAAAACYQpgAAAADABMIUAAAAAJhAmAIAAAAAEwhTAAAAAGACYQoAAAAATCBMAQAAAIAJhCkAAAAAMOGpClNRUVEKCgrSkiVL7F0KAAAAgOfcUxOm4uPj1aNHD926dcvepQAAAADA0xOmxo4dK09PT3uXAQAAAACSnpIwtWvXLi1YsECDBw+2dykAAAAAIElysXcBj3Lt2jX17NlTX331lfLmzZshbRqG4TDDBS0Wi71LsGEYhr1LSCE2NtbmX8De6JNwNPRJ2Nv9xzNxcXHy8PBQXFzcEz3WsVgscnd3V6KRpMSkxCe23tQkGkmS7v5d2vv4ymKxyMPDwyH2i2SfffOg10nDMNLVRx0+TPXr109BQUF68803M6zN+Ph4HTlyJMPaM8vV1VUl/P3l4uxs71IkSQmJiTp86JDi4+PtXUqqjh8/bu8SABv0STga+iTsIbXjGQ8PD+XIkcNuNd25fVu3btr3g/PbnncPs6Oiouz+QYeHh4dKlCih27H23y+SffdNaq+Tbm5upttz6DC1dOlS7d69W8uXL8/Qdl1dXVWkSJEMbdMMi8UiF2dnzVv7uy5E27dj586ZWe/XLaaiRYva/dOT+8XGxur48eMqWLCgPDw87F0OQJ+Ew6FPwp5SO54xEpN0Jy5OmdzcZHF+cleV+L2cU/Uq+cjd3V2ZsyQ8sfWmxt3DXZLk4+Nj92Or5DMv7h723y/JdUhPdt886HXy6NGj6WrXocPU4sWLdfnyZdWsWdNmet++fbVy5UpNnTrVVLsWi0WZM2fOgAozxuWY2zp/2b6fWDhb7r7QOfKbsIeHh0M9bwB9Eo6GPgl7uvd4JjEpUbdu3lLmLJnl7PTkRuB458wiSXKyOD3R9abGEY+tnB1gvyTXIdln39z/OpneYagOHaaGDRum27dv20yrW7euOnXqpIYNG9qpKgAAAABw8DCVJ0+eVKd7eXk9cB4AAAAAPAlPxa3RAQAAAMDROPSZqdT85z//sXcJAAAAAMCZKQAAAAAwgzAFAAAAACYQpgAAAADABMIUAAAAAJhAmAIAAAAAEwhTAAAAAGACYQoAAAAATCBMAQAAAIAJhCkAAAAAMIEwBQAAAAAmEKYAAAAAwATCFAAAAACYQJgCAAAAABMIUwAAAABgAmEKAAAAAEwgTAEAAACACYQpAAAAADCBMAUAAAAAJhCmAAAAAMAEwhQAAAAAmECYAgAAAAATCFMAAAAAYAJhCgAAAABMIEwBAAAAgAmEKQAAAAAwgTAFAAAAACYQpgAAAADABMIUAAAAAJhAmAIAAAAAEwhTAAAAAGACYQoAAAAATCBMAQAAAIAJhCkAAAAAMIEwBQAAAAAmEKYAAAAAwATCFAAAAACYQJgCAAAAABMIUwAAAABgAmEKAAAAAEwgTAEAAACACYQpAAAAADCBMAUAAAAAJhCmAAAAAMAEwhQAAAAAmECYAgAAAAATCFMAAAAAYAJhCgAAAABMIEwBAAAAgAmEKQAAAAAwgTAFAAAAACYQpgAAAADABMIUAAAAAJhAmAIAAAAAEwhTAAAAAGACYQoAAAAATCBMAQAAAIAJhCkAAAAAMIEwBQAAAAAmEKYAAAAAwATCFAAAAACYQJgCAAAAABMIUwAAAABgAmEKAAAAAEwgTAEAAACACYQpAAAAADCBMAUAAAAAJhCmAAAAAMAEwhQAAAAAmECYAgAAAAATCFMAAAAAYAJhCgAAAABMIEwBAAAAgAmEKQAAAAAwgTAFAAAAACYQpgAAAADABMIUAAAAAJjwVISpmJgYhYeHq3r16ipTpozef/997d69295lAQAAAHiOPRVhqlu3btq3b59GjBihxYsXq3jx4mrdurWOHTtm79IAAAAAPKccPkydOHFCW7duVb9+/VSuXDn5+PioT58+yp07t5YvX27v8gAAAAA8pxw+TOXMmVOTJ09WQECAdZrFYpHFYtG1a9fsWBkAAACA55mLvQt4lGzZsqlGjRo209asWaMTJ07oH//4h6k2DcPQrVu3MqK8dLFYLPLw8FCikaTEpES71pJoJEmSYmNjZRiGXWu5X2xsrM2/gL3RJ+Fo6JOwp9SOZ5ISk2z+fVIS9f/r5djKhiMdc0r22TcPep00DEMWi8V0uw4fpu63d+9effHFF6pbt65q1qxpqo34+HgdOXIkYwszwcPDQyVKlNDt2Nu6ddO+4e62592uEBUV5bBvxsePH7d3CYAN+iQcDX0S9vCw45nbt28/0VribsdJku7cucOx1T0c6ZhTsu++Se110s3NzXR7T1WYWrdunXr06KEyZcpo2LBhpttxdXVVkSJFMrAyc5JTsLuHuzJnSbBrLe4e7pIkHx8fu396cr/Y2FgdP35cBQsWlIeHh73LAeiTcDj0SdhTasczSYlJun37ttzd3eXk/OSuKnFzv3tQnClTJmXOkvmJrTc1jnRs5UjHnMl1SE923zzodfLo0aPpavepCVOzZ8/WN998o3r16mnIkCHpSpAWi0WZM9v3D+xezhYnOTs5270GSQ79Juzh4eFQzxtAn4SjoU/CnlI7nnFyfrLHOM7/fzsAJ46tUuUIx5zJdUj22Tf3v06mZ4if9BTcgEKS5s6dq4EDB6p58+YaMWJEuoIUAAAAAGQEhz8zFRUVpW+//VZ16tRRu3btdOnSJes8d3d3Zc2a1Y7VAQAAAHheOXyYWrNmjeLj4/Xzzz/r559/tpnXuHFjDR482E6VAQAAAHieOXyY+vTTT/Xpp5/auwwAAAAAsPFUXDMFAAAAAI6GMAUAAAAAJhCmAAAAAMAEwhQAAAAAmECYAgAAAAATCFMAAAAAYAJhCgAAAABMIEwBAAAAgAmEKQAAAAAwgTAFAAAAACYQpgAAAADABMIUAAAAAJhAmAIAAAAAEwhTAAAAAGACYQoAAAAATCBMAQAAAIAJhCkAAAAAMIEwBQAAAAAmEKYAAAAAwATCFAAAAACYQJgCAAAAABMIUwAAAABgAmEKAAAAAEwgTAEAAACACYQpAAAAADCBMAUAAAAAJhCmAAAAAMAEwhQAAAAAmECYAgAAAAATCFMAAAAAYAJhCgAAAABMIEwBAAAAgAmEKQAAAAAwgTAFAAAAACYQpgAAAADABMIUAAAAAJhAmAIAAAAAEwhTAAAAAGACYQoAAAAATCBMAQAAAIAJhCkAAAAAMIEwBQAAAAAmEKYAAAAAwATCFAAAAACYQJgCAAAAABMIUwAAAABgAmEKAAAAAEwgTAEAAACACYQpAAAAADCBMAUAAAAAJhCmAAAAAMAEwhQAAAAAmECYAgAAAAATCFMAAAAAYAJhCgAAAABMIEwBAAAAgAmEKQAAAAAwgTAFAAAAACYQpgAAAADABMIUAAAAAJhAmAIAAAAAEwhTAAAAAGACYQoAAAAATCBMAQAAAIAJhCkAAAAAMIEwBQAAAAAmEKYAAAAAwATCFAAAAACYQJgCAAAAABMIUwAAAABgAmEKAAAAAEwgTAEAAACACYQpAAAAADCBMAUAAAAAJhCmAAAAAMAEwhQAAAAAmPBUhKmkpCSNGTNG1apVU+nSpfXJJ5/o5MmT9i4LAAAAwHPsqQhTERERmjt3rgYOHKj58+crKSlJbdq0UVxcnL1LAwAAAPCccvgwFRcXp++//16dOnVSzZo1VaxYMY0cOVLnzp3T2rVr7V0eAAAAgOeUw4ep33//XTdv3lSlSpWs07Jly6YSJUpo165ddqwMAAAAwPPMYhiGYe8iHmbt2rXq2LGjDhw4IHd3d+v0zp076/bt25o0aVKa2tu7d68Mw5Crq2tGl2qKxWLRjdh4JSXZ92lwcrLI08NVjtgdDMNQYmKinJ2dZbFY7F0OQJ+Ew6FPwt7uP54xDEOGJMv/z3tSXF2c5JHJRTdj45XIsZUNRznmlOyzbwzDUEJCglxcXGz6ZHx8vCwWi8qUKWOqXZeMKvDvEhsbK0lyc3OzmZ4pUyZdvXo1ze0l7zxHerPx9HCMYCc51n5JZrFY5OTk8CdR8RyhT8LR0CfhCBzpeCaLA9XiSMdWjvQcSU9231gslhR5Inl6eupw+DCVfDYqLi7O5szUnTt35OHhkeb2goKCMqw2AAAAAM8vh/8YK2/evJKkCxcu2Ey/cOGC8uTJY4+SAAAAAMDxw1SxYsXk6empHTt2WKddu3ZNhw8fVvny5e1YGQAAAIDnmcMP83Nzc1OLFi00bNgw5cqVS/nz59fQoUPl7e2tunXr2rs8AAAAAM8phw9TktSpUyclJCToq6++0u3bt1W+fHlNmzbNYe7IBwAAAOD54/C3RgcAAAAAR+Tw10wBAAAAgCMiTAEAAACACYQpAAAAADCBMAUAAAAAJhCmAAAAAMAEwhQAAAAAmECYAgAAAAATCFOwu6SkJI0ZM0bVqlVT6dKl9cknn+jkyZMPXP6///2v2rZtq4oVK6pSpUrq1KmTzpw58wQrxrMurX3yXsuWLZOfn59OnTr1N1eJ50la+2R8fLyGDx9uXb5FixY6cuTIE6wYz7q09snLly+re/fuevXVV1WxYkV17dpV58+ff4IV43kzadIktWzZ8qHLREdHq3v37ipfvrwqVKig/v37KzY2Nk3rIUzB7iIiIjR37lwNHDhQ8+fPV1JSktq0aaO4uLgUy0ZHR+vjjz+Wu7u7Zs2apSlTpujKlStq06aN7ty5Y4fq8SxKS5+81+nTpzVgwIAnVCWeJ2ntk/369dOSJUv07bffavHixcqVK5c++eQTXb9+/QlXjmdVWvtkly5ddObMGU2fPl3Tp0/XmTNnFBYW9oSrxvNizpw5GjVq1COX69Spk06cOKEZM2Zo9OjR2rx5s/r165e2lRmAHd25c8cICgoy5syZY5129epVIzAw0Fi+fHmK5X/44QcjKCjIiI2NtU47c+aM4evra/z6669PpGY829LaJ5MlJiYa77//vvHBBx8Yvr6+xsmTJ59EuXgOpLVP/vXXX4afn5+xceNGm+WDg4N5nUSGSGufvHr1quHr62usX7/eOm3dunWGr6+vER0d/SRKxnPi3LlzRrt27YzSpUsb9erVM1q0aPHAZffu3Wv4+voaR48etU775ZdfDD8/P+PcuXOPvU7OTMGufv/9d928eVOVKlWyTsuWLZtKlCihXbt2pVi+UqVKioiIkLu7u3Wak9Pdbnzt2rW/v2A889LaJ5NNnDhR8fHxateu3ZMoE8+RtPbJrVu3KmvWrKpevbrN8hs2bLBpAzArrX3S3d1dWbJk0dKlS3Xjxg3duHFDkZGR8vHxUbZs2Z5k6XjGHTp0SK6urlq2bJlKlSr10GV3796tF198UYULF7ZOq1ChgiwWi/bs2fPY63QxXS2QAc6dOydJyps3r8303LlzW+fdq0CBAipQoIDNtMmTJ8vd3V3ly5f/+wrFcyOtfVKS/v3vf+v777/XokWLuAYAGS6tfTIqKkovvfSS1q5dq8mTJ+v8+fMqUaKEevfubXPQAJiV1j7p5uamwYMHKzw8XOXKlZPFYlHu3Lk1e/Zs6weiQEYICQlRSEjIYy17/vz5FH3Yzc1NOXLk0NmzZx97nfRg2FXyRX5ubm420zNlyvRY10DNmjVLs2fPVo8ePZQrV66/pUY8X9LaJ2/duqUePXqoR48eKliw4JMoEc+ZtPbJGzdu6MSJE4qIiFC3bt00YcIEubi4qFmzZrp8+fITqRnPtrT2ScMwdOTIEQUFBWnOnDn65z//qXz58qlDhw66cePGE6kZuF9sbGyKPiw9/jFoMsIU7Cp5uN79F6zeuXNHHh4eD3ycYRgaNWqUvv76a7Vv3/6Rd2sBHlda++TXX38tHx8fvffee0+kPjx/0tonXVxcdOPGDY0cOVJVq1ZVYGCgRo4cKUn68ccf//6C8cxLa59ctWqVZs+eraFDh6ps2bKqUKGCJk6cqNOnT2vRokVPpGbgfu7u7qneMOXOnTvKnDnzY7dDmIJdJZ9evXDhgs30CxcuKE+ePKk+Jj4+Xp9//rkmTpyoL774Ql26dPm7y8RzJK19cvHixfr1118VFBSkoKAgffLJJ5KkBg0aaOLEiX9/wXjmpbVPent7y8XFxWZIn7u7u1566SVu2Y8MkdY+uXv3bvn4+MjT09M6LXv27PLx8dGJEyf+3mKBB/D29k7Rh+Pi4hQTE6PcuXM/djuEKdhVsWLF5OnpqR07dlinXbt2TYcPH37gNVA9e/bU6tWrNXz4cH300UdPqFI8L9LaJ9euXasVK1Zo6dKlWrp0qb7++mtJd6/l42wVMkJa+2T58uWVkJCggwcPWqfdvn1bJ0+e1CuvvPJEasazLa190tvbWydOnLAZOnXr1i2dOnWK4dGwm/Lly+vcuXM2gX7nzp2SpLJlyz52O9yAAnbl5uamFi1aaNiwYcqVK5fy58+voUOHytvbW3Xr1lViYqKuXLmirFmzyt3dXUuWLNHKlSvVs2dPVahQQRcvXrS2lbwMkB5p7ZP3H5wmX3ydL18+5ciRww5bgGdNWvtkuXLlVLlyZfXq1UsDBgxQjhw5NGbMGDk7O+utt96y9+bgGZDWPtmoUSNNmzZNXbp0UefOnSVJo0aNUqZMmRQaGmrnrcHz4v5+WapUKZUpU0Zdu3ZVv379dOvWLYWHh6tRo0YPHB2VGs5Mwe46deqkpk2b6quvvtL7778vZ2dnTZs2Ta6urjp79qyqVq2qlStXSpJWrFghSfruu+9UtWpVm5/kZYD0SkufBJ6EtPbJsWPHqkKFCvrss8/UtGlT3bhxQzNnzuRGPcgwaemTuXPn1ty5c2UYhj788EN9/PHHcnV11dy5c5U1a1Y7bwmeF/f3S4vFonHjxqlAgQL68MMP1aVLF1WvXj3NX9prMQzD+BvqBQAAAIBnGmemAAAAAMAEwhQAAAAAmECYAgAAAAATCFMAAAAAYAJhCgAAAABMIEwBAAAAgAmEKQDAc41vCAEAmEWYAoDnVMuWLeXn52fzU7JkSdWsWVP9+/fX1atXM2Q9ISEh6t27d7rbWbJkifz8/HTq1KkHLnPq1Cn5+flpyZIlku5+ea2fn591fsuWLdWyZUvr7wsXLtSQIUPSVdeOHTtS7MfkfVm9enX17NlTFy9eTNc6AACOycXeBQAA7KdEiRLq27ev9ff4+HgdOnRII0aM0JEjRzRv3jxZLBY7Vpg2uXPn1oIFC/Tyyy+nOv/ebZWkCRMmqEKFChmy7vDwcPn7+1t/v3nzpvbs2aPJkycrKipKCxcuzJD1AAAcB2EKAJ5jnp6eKl26tM208uXL6+bNmxozZowOHDiQYr4jc3Nze2i9RYoU+dvWXaRIkRTrrlKliuLi4jRlyhQdPXr0b10/AODJY5gfACCFkiVLSpLOnDkj6e7wuB49eqhTp04qXbq0Pv74Y0nS9evXNWjQINWuXVsBAQFq0KCBFi1alKK9+Ph4ff311ypfvrzKlSunXr166cqVKzbLLFy4UKGhoSpdurQCAwP11ltvadWqVSna2rt3rxo1aqSSJUuqQYMGWrlypXXe/cP87nfvML+QkBCdPn1aP/74o/z8/HTo0CEFBARoxIgRNo+JjY1V2bJlNWHChMfdfTayZcsmSTZn+NatW6dmzZopKChIJUuWVL169TRnzhzr/OShg9u2bVOrVq1UqlQpValSRUOHDlViYqJ1uRs3big8PFyVKlVSUFCQunbtqhkzZtgMbUxeX2hoqAICAlSlShV9/fXXunXrlqntAQD8D2EKAJBCVFSUJOmll16yTlu1apWyZMmiCRMmqE2bNrp9+7aaNWum5cuXq02bNoqIiFDZsmX15ZdfauLEiTbtrVq1SocOHdLgwYPVq1cvbdq0SZ988ok1GMyZM0fh4eGqXbu2Jk2apGHDhsnNzU09evTQuXPnbNoKDw/X66+/roiICBUtWlRdu3bVunXr0ryN48aN04svvqgaNWpowYIFKlq0qGrXrq3ly5fb3JTi559/1q1bt9SoUaOHtpeUlKSEhATrT0xMjNauXatp06YpMDBQPj4+kqRNmzYpLCxM/v7+ioiI0NixY/XSSy9pwIABOnDggE2bPXr0UNmyZTVx4kQ1aNBAU6dOtRku2KFDB61atUodO3bUyJEjdfPmTQ0fPtymjeXLlyssLEyFChXS+PHj9dlnn2nZsmXq0KEDN98AgHRimB8APMcMw1BCQoL196tXr2rnzp2aMGGC9axJMldXV/Xv319ubm6SpLlz5+qPP/7Q/PnzFRQUJEmqVq2aEhISFBERoffee085cuSQJOXMmVPTpk1T5syZrb+HhYVpy5YtCg4O1smTJ9W6dWt16NDBur78+fMrNDRUe/bs0RtvvGGd3rFjR7Vu3VqSVL16dR0/flwRERGqXbt2mra9RIkScnNzU65cuazD85o0aaKVK1dqx44devXVVyVJS5cuVeXKlZU3b96HtvfRRx+lmJY9e3bVqlVLn3/+uZyc7n5+efToUTVu3FhffvmldbmgoCBVrFhRO3bsUKlSpazT3377bYWFhUmSKlWqpHXr1mnTpk167733tG3bNu3YsUNjx45V3bp1rfujQYMG+vPPPyXdfX6HDRumatWqadiwYdZ2CxYsqI8++kibN29WzZo107TfAAD/Q5gCgOfYrl27bG6aIElOTk6qXLmyBgwYYDM0rVChQtYgJUk7d+5U/vz5rUEqWcOGDbVo0SIdOHBANWrUkCTVqFHDGqSku0PsXFxctGvXLgUHB1vv9nft2jUdO3ZMJ06c0I4dOyRJcXFxNu3Xr1/f5vfatWtr7NixunnzptndYFW5cmXly5dPkZGRevXVV3Xu3Dlt27ZNQ4cOfeRj+/fvL39/fyUlJWn9+vWaOnWqWrZsqY4dO9os16ZNG0l3b1ARFRWlv/76SwcPHpSUclvv37fe3t7W4Xnbt2+Xq6urTYh0cnJS/fr1NXbsWEnSsWPHdO7cObVr184mNJcvX16enp7aunUrYQoA0oEwBQDPMX9/f/Xv31/S3Wt6MmXKpLx588rT0zPFslmyZLH5/erVq3rxxRdTLPfCCy9IuhuMkt2/nJOTk3LmzGld5q+//lJ4eLi2bdsmV1dXFSpUSMWKFZOU8nugkttP5uXlJcMwdOPGjcfa5odxcnJSaGiopk+frr59+yoyMlKenp6qU6fOIx/r4+OjgIAASVKpUqXk6uqqcePGKVOmTGrbtq11uStXrqhv375at26dLBaLXnnlFZUrV05Sym11d3dPUV/yMtHR0cqRI4f1jFcyLy8v6/9jYmIk3Q16yc/zvS5cuPDI7QIAPBhhCgCeY1myZLEGgLTKnj27Tpw4kWJ68ncq5cyZ0zot+aA+WWJioqKjo+Xl5aWkpCS1bdtWrq6uWrRokYoXLy4XFxcdPXpUkZGRKdq/evWqTaC6dOmSnJ2dlT17dl26dMnUttwrNDRU48eP15YtW7Rq1SrVr19fmTJlSnM77du317p16zRmzBjVrFlTvr6+ku5eB3Xs2DHNmDFDQUFBcnNzU2xsrH744Yc0tZ8nTx5FR0crKSnJJlBdvnzZ+v/km1/07Nkz1VvAZ8+ePc3bBQD4H25AAQAwpXz58jp9+rT27dtnM33ZsmVydXVVYGCgddrWrVtthpmtWbNGCQkJqlixoqKjoxUVFaWmTZsqICBALi53P+fbsmWLpLs3drjXpk2brP9PSkrS6tWrVapUqRRncR7H/Wd1pLvXalWqVEkzZ87UkSNHFBoamuZ2JcnFxUX9+vVTQkKCvv76a+v0PXv2qG7duqpYsaJ12OSDtvVhKlSooISEBG3YsME6zTAMm5txFCpUSF5eXjp16pQCAgKsP3ny5NHw4cN1+PBhU9sGALiLM1MAAFNCQ0M1d+5chYWFqVOnTipQoIA2bNigxYsX67PPPrOeFZHunq3q2LGjWrZsqePHj2vEiBGqUqWKKlWqJIvFovz582vOnDny9vZWtmzZ9Msvv2jmzJmS7t6a/F6jRo1SYmKi8ubNq3nz5ikqKkrTp083tQ3ZsmXT4cOHtXPnTgUGBloDWdOmTdWtWzcVLlzY5oYQaRUUFKSGDRsqMjJSq1at0uuvv67AwEAtX75c/v7+8vb21t69ezV58mRZLJYU2/ow5cuXV5UqVfTll1/q0qVLypcvnxYtWqT//Oc/1mvdnJ2d1bVrV4WHh8vZ2VnBwcG6du2aIiIidP78+RTXywEA0oYzUwAAUzw8PDRr1iwFBwdr9OjRat++vfbs2aNvvvkmxU0XmjVrJi8vL4WFhWn06NF68803NW7cOOtBf0REhPLkyaPevXurS5cuOnDggCZMmKBChQpp9+7dNm0NGjRIM2fOVIcOHXT+/HlNmTIl1SFsj6NVq1a6dOmSWrdurd9++806vUaNGrJYLKbPSt2rR48eypIli7777jvFxsZq8ODBKlWqlAYOHKiwsDCtX79e/fv3V9WqVVNs66OMHDlSISEhGj58uDp37iw3Nze9//77Njf7ePvttzV8+HDt3btXn376qfr166cCBQpo1qxZNre+BwCkncXgSyYAALCxcuVK9ezZU5s3b7a5oYMjOX36tPbv369atWrZDHHs1KmTTp48qR9//NGO1QHA84FhfgAA/L9169bp4MGDmj9/vkJDQx02SEl3r/fq3bu3atWqpaZNm8rZ2Vm//PKL1q5dq0GDBtm7PAB4LnBmCgCA/zdjxgyNGjVKZcuW1ahRo5Q1a1Z7l/RQ27dv1/jx43XkyBElJCSocOHC+vjjj9WgQQN7lwYAzwXCFAAAAACYwA0oAAAAAMAEwhQAAAAAmECYAgAAAAATCFMAAAAAYAJhCgAAAABMIEwBAAAAgAmEKQAAAAAwgTAFAAAAACYQpgAAAADAhP8DlhXE5FOBE08AAAAASUVORK5CYII=", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "sns.histplot(account_diff_df['range'], bins=20)\n", "plt.title('Probability Range Across Accounts for Each (CMS Document, Fragment)')\n", "plt.xlabel('Probability Range')\n", "plt.ylabel('Frequency')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 12, "id": "8c6d90e5-a7f7-4813-a0f7-4f94a2065659", "metadata": {}, "outputs": [], "source": ["group_sizes = df.groupby(['cmsDocumentId', 'fragmentId'])['accountId'].nunique()\n", "valid_groups = group_sizes[group_sizes >= 2].index\n", "\n", "filtered_df = df.set_index(['cmsDocumentId', 'fragmentId']).loc[valid_groups].reset_index()\n"]}, {"cell_type": "code", "execution_count": 13, "id": "c522c0fd-92cf-44ba-b39a-f4d433c3b14a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   cmsDocumentId  fragmentId      mean       std       min       max     range\n", "0     ADC-102760           1  0.143180  0.173444  0.000013  0.940808  0.940795\n", "1     ADC-102760           2  0.143180  0.173444  0.000013  0.940808  0.940795\n", "2     ADC-102760           3  0.143180  0.173444  0.000013  0.940808  0.940795\n", "3     ADC-102760           4  0.145656  0.167816  0.000021  0.957097  0.957076\n", "4     ADC-102761           1  0.099151  0.060692  0.029436  0.140196  0.110761\n", "5     ADC-102761           2  0.099151  0.060692  0.029436  0.140196  0.110761\n", "6     ADC-102761           3  0.099151  0.060692  0.029436  0.140196  0.110761\n", "7     ADC-102761           4  0.124066  0.064947  0.054881  0.183722  0.128841\n", "8      ADC-94563           1  0.151357  0.176716  0.000015  0.875111  0.875096\n", "9      ADC-94563           2  0.151357  0.176716  0.000015  0.875111  0.875096\n", "10     ADC-94563           3  0.151357  0.176716  0.000015  0.875111  0.875096\n", "11     ADC-94563           4  0.168477  0.185984  0.000044  0.878294  0.878250\n", "12     ADC-94563           5  0.150750  0.173532  0.000024  0.838277  0.838253\n", "13     ADC-94575           1  0.226885  0.223405  0.000005  0.963774  0.963769\n", "14     ADC-94575           2  0.226885  0.223405  0.000005  0.963774  0.963769\n", "15     ADC-94575           3  0.205982  0.204365  0.000004  0.961227  0.961223\n", "16     ADC-94575           4  0.205982  0.204365  0.000004  0.961227  0.961223\n", "17     ADC-94590           1  0.135494  0.170805  0.000170  0.833844  0.833674\n", "18     ADC-94590           2  0.135494  0.170805  0.000170  0.833844  0.833674\n", "19     ADC-94590           3  0.132367  0.167064  0.000135  0.822830  0.822694\n"]}], "source": ["stats_df = filtered_df.groupby(['cmsDocumentId', 'fragmentId'])['probability'].agg(['mean', 'std', 'min', 'max'])\n", "\n", "stats_df['range'] = stats_df['max'] - stats_df['min']\n", "stats_df = stats_df.reset_index()\n", "\n", "stats_df = stats_df.dropna()\n", "\n", "print(stats_df.head(20))\n"]}, {"cell_type": "code", "execution_count": 14, "id": "d9290f83-c469-40c3-b5cf-205a845da74f", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(12, 6))\n", "sns.stripplot(data=stats_df, x='max', y='cmsDocumentId', hue='fragmentId', jitter=0.25)\n", "plt.title('Spread of Probability Ranges Across Accounts (Valid Groups Only)')\n", "plt.xlabel('Probability Range')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 15, "id": "62b7821c-17e3-46cf-8de6-49532401dbfb", "metadata": {}, "outputs": [], "source": ["# x-axis: # of accts with specific frag being winner\n", "# y-axis: doc"]}, {"cell_type": "code", "execution_count": 16, "id": "b51a0314-01a0-4dcd-a3fd-d06fcea5e6d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   cmsDocumentId  fragmentId  num_accounts\n", "0     ADC-102760           1          6078\n", "1     ADC-102760           2          6078\n", "2     ADC-102760           3          6078\n", "3     ADC-102760           4          9984\n", "4     ADC-102761           4             3\n", "5      ADC-94563           1           293\n", "6      ADC-94563           2           293\n", "7      ADC-94563           3           293\n", "8      ADC-94563           4           908\n", "9      ADC-94563           5           204\n", "10     ADC-94575           1          2958\n", "11     ADC-94575           2          2958\n", "12     ADC-94575           3          1541\n", "13     ADC-94575           4          1541\n", "14     ADC-94590           1            75\n", "15     ADC-94590           2            75\n", "16     ADC-94590           3            69\n", "17     ADC-94590           4            69\n", "18     ADC-96117           1           544\n", "19     ADC-96117           2           544\n", "20     ADC-96117           3           544\n", "21     ADC-96117           4           544\n", "22     ADC-96117           5           544\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "df['max_prob'] = df.groupby(['accountId', 'cmsDocumentId'])['probability'].transform('max')\n", "winners = df[df['probability'] == df['max_prob']].copy()\n", "\n", "winner_counts = winners.groupby(['cmsDocumentId', 'fragmentId'])['accountId'].nunique().reset_index()\n", "winner_counts.rename(columns={'accountId': 'num_accounts'}, inplace=True)\n", "\n", "print(winner_counts)\n", "\n", "plt.figure(figsize=(12, 6))\n", "sns.swarmplot(\n", "    data=winner_counts,\n", "    x='num_accounts',\n", "    y='cmsDocumentId',\n", "    hue='fragmentId',\n", "    dodge=True,\n", "    palette='deep'\n", ")\n", "\n", "plt.title('Number of Accounts per Document with Fragment as Winner')\n", "plt.xlabel('Number of Accounts')\n", "plt.ylabel('CMS Document ID')\n", "plt.legend(title='Fragment ID', bbox_to_anchor=(1.05, 1), loc='upper left')\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "e4fded3e-0f77-40da-91a9-469811b949cf", "metadata": {}, "source": ["# examples"]}, {"cell_type": "code", "execution_count": 17, "id": "cd8382f8-4dc8-4b97-a5cf-b682aea5d411", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "\n", "valid_accounts = df[df.probability > 0.3]\n", "sample_account = np.random.choice(valid_accounts.accountId)\n", "\n", "account_df = df[df['accountId'] == sample_account].copy()\n", "account_df['fragment'] = account_df['cmsDocumentId'].astype(str) + \"_\" + account_df['fragmentId'].astype(str)\n", "\n", "account_df = account_df.sort_values(by='probability', ascending=False)\n", "\n", "plt.figure(figsize=(12, 6))\n", "sns.barplot(data=account_df, x='fragment', y='probability', palette='Blues_d')\n", "plt.xticks(rotation=45)\n", "plt.title(f'Variation of Scores Given by Account {sample_account}')\n", "plt.xlabel('CMS Document + Fragment')\n", "plt.ylabel('Probability Score')\n", "plt.tight_layout()\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}