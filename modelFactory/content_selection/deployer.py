import logging
from datetime import datetime

import pandas as pd

from abstract_model_factory.abstract_deployer import AbstractDeployer
from content_selection.constants import Constants
from content_selection.data import Data
from content_selection.data_access_layer import DataAccessLayer

from sqlalchemy import create_engine

class Deployer(AbstractDeployer):

    def execute(self):
        print("Executing Deployer")
        if Constants.LEARNING_MODE:
            self.write_model()
        if Constants.EXECUTION_MODE:
            self.write_predictions_to_s3()
            self.write_predictions_to_rds()

    def qa_module(self):
        pass

    # Save results to S3
    def write_predictions_to_s3(self):
        data = Data.get_instance()
        prediction_results = data.get_dataframe("prediction_results")

        runmonth = data.get_param("runmonth")
        rundate = data.get_param("rundate")
        data_access_layer = DataAccessLayer()
        predictions_s3_dir = Constants.TARGET_S3_PATH + f"predictions/runmonth={runmonth}/"
        target_name = f"results_{rundate}.parquet"
        data_access_layer.write_predictions_to_s3(prediction_results, predictions_s3_dir, target_name)

    def create_mysql_engine(self, db_name):
        if Constants.LOCAL_MODE:
            engine = create_engine(f"mysql+mysqlconnector://{Constants.rds_username}:{Constants.rds_password}@localhost:3337/{db_name}")
        else:
            engine = create_engine(f"mysql+mysqlconnector://{Constants.rds_username}:{Constants.rds_password}@{Constants.rds_hostname}:{Constants.rds_port}/{db_name}")

        return engine

    # Save results to RDS table
    def write_predictions_to_rds(self):
        data = Data.get_instance()
        prediction_results = data.get_dataframe("prediction_results")

        table_name = Constants.CONTENT_SELECTION_OUTPUT_TABLE

        engine = None
        try:
            engine = self.create_mysql_engine(Constants.learning_db)

            # Truncate table first using engine connection
            print(f"Truncating table {table_name}...")
            with engine.connect() as connection:
                connection.execute(f"TRUNCATE TABLE {table_name}")
            print(f"Successfully truncated table {table_name}")

            # Write predictions to RDS table using SQLAlchemy engine
            print(f"Writing {len(prediction_results)} rows to RDS table {table_name}...")
            prediction_results.to_sql(
                name=table_name,
                con=engine,
                if_exists='append',
                index=False,
                method='multi'
            )

            print(f"Successfully wrote predictions to RDS table {table_name}")

        except Exception as e:
            print(f"Error writing predictions to RDS: {str(e)}")
            raise e
        finally:
            if engine:
                engine.dispose()
                print("Database engine disposed")

    def write_model(self):
        data = Data.get_instance()
        model = data.get_model("xgb_model")

        model_s3_path = DataAccessLayer.get_xgb_model_s3_path()
        DataAccessLayer.write_xgb_model_to_s3(model, model_s3_path)

        self.write_model_latest_csv(model_s3_path)

    def write_model_latest_csv(self, model_s3_path):
        data = Data.get_instance()

        # Get evaluation results if available
        evaluation_results = data.get_param("evaluation_results", {})

        # Prepare latest model information
        latest_dict = {
            'xgb_model_path': [model_s3_path],
            'runmonth': [data.get_param("runmonth", "unknown")],
            'timestamp': [datetime.now().strftime('%Y-%m-%d %H:%M:%S')]
        }

        # Add evaluation metrics if available
        if evaluation_results:
            latest_dict['test_auc'] = [evaluation_results.get('test_auc', 'N/A')]
            latest_dict['train_auc'] = [evaluation_results.get('train_auc', 'N/A')]
            latest_dict['auc_gap'] = [evaluation_results.get('auc_gap', 'N/A')]
            latest_dict['test_f1'] = [evaluation_results.get('test_f1', 'N/A')]
        else:
            latest_dict['test_auc'] = ['N/A']
            latest_dict['train_auc'] = ['N/A']
            latest_dict['auc_gap'] = ['N/A']
            latest_dict['test_f1'] = ['N/A']

        # Create DataFrame and write to S3
        latest_pdf = pd.DataFrame(latest_dict)
        latest_csv_path = Constants.TARGET_S3_PATH + "models/latest.csv"
        DataAccessLayer.write_latest_csv(latest_csv_path, latest_pdf)

        print(f"Written model metadata to latest.csv: {latest_csv_path}")


