
from abstract_model_factory.abstract_loader import AbstractLoader
from content_selection.data import Data
from content_selection.data_access_layer import DataAccessLayer
from qa_data_handler import <PERSON><PERSON>ata<PERSON><PERSON><PERSON>
from content_selection.constants import Constants
import os



class Loader(AbstractLoader):

    def qa_module(self):
        pass

    def read_training_data(self):
        data = Data.get_instance()
        col_str = ', '.join(Constants.TRAINING_FEATURES_FROM_RDS)
        rundate = data.get_param("rundate")
        sql = f"""select {col_str} from {Constants.learning_db}.Content_Selection_Input_v
        where eventDate between DATE_SUB('{rundate}', INTERVAL 180 DAY) and DATE_SUB('{rundate}', INTERVAL 5 DAY)"""
        df = DataAccessLayer.read_rds_table_as_pandas(sql)

        data.set_dataframe("training_df", df)

    def read_prediction_data(self):
        data = Data.get_instance()
        rundate = data.get_param("rundate")

        sql = f'''
        SELECT DISTINCT
            sdrdsd.runRepDateSuggestionDetailId,
            sdrds.accountId,
            CONCAT_WS('-', fb.cmsDocumentId, fb.fragmentBundleId) AS fragmentId,
            actp.contentTopicName,
            fb.supportsSample,
            CASE
                WHEN actp.contentTopicOrder > 4 THEN 'Intermediate'
                ELSE 'Introductory'
            END AS contentCategory,
            a.specialties_std_akt,
            a.behaviorSegment_akt,
            a.coverageChange_akt,
            a.cur1m_Coverage_akt,
            a.targetType_akt,
            a.ncoAudience_akt,
            a.sfmcHCPeligible_akt,
            a.ncoWebSampleEligible_akt,
            SUBSTRING_INDEX(
                SUBSTRING_INDEX(f.geoLocationString, ',', 1), ' ', -1
            ) AS state,
            acs.Credentials_vod__c,
            acs.ADC_Commercial_CGM_Decile__c,
            acs.ADC_Subtype__c,
            acs.ADC_Opportunities__c,
            cstt.message_tone AS tone
        FROM
            {Constants.engine_db}.DCODSERunMap rmap USE INDEX (DCODSERunMap_runDate)
            INNER JOIN {Constants.engine_db}.SparkDSERun r ON rmap.dseRunId = r.runId
            INNER JOIN {Constants.engine_db}.SparkDSERunRep rr ON r.runUID = rr.runUID
            INNER JOIN {Constants.engine_db}.SparkDSERunRepDate rrd ON rrd.runUID = r.runUID AND rrd.repId = rr.repId
            INNER JOIN {Constants.engine_db}.SparkDSERunRepDateSuggestion sdrds ON sdrds.runRepDateId = rrd.runRepDateId
            INNER JOIN {Constants.engine_db}.SparkDSERunRepDateSuggestionDetail sdrdsd ON sdrdsd.runRepDateSuggestionId = sdrds.runRepDateSuggestionId
            INNER JOIN {Constants.learning_db}.Message_CMSMessage_mapping_v map ON map.stableMessageUid = sdrdsd.messageUID
            INNER JOIN {Constants.stage_db}.AKT_Content_Fragment_Bundle_V fb ON fb.cmsDocumentId = map.cmsMessageUid
            INNER JOIN {Constants.engine_db}.Account a ON a.accountId = sdrds.accountId
            INNER JOIN {Constants.stage_db}.AKT_Content_Target_Plan actp ON fb.cmsDocumentId = actp.cmsDocumentId
            INNER JOIN {Constants.engine_db}.Facility f ON a.facilityId = f.facilityId
            INNER JOIN {Constants.cs_db}.Account acs ON acs.Id = a.externalId
            INNER JOIN {Constants.learning_db}.Content_Selection_Tone_temp cstt
                ON cstt.cmsDocumentId = fb.cmsDocumentId AND cstt.fragmentBundleId = fb.fragmentBundleId
        WHERE
            rmap.runDate = '{rundate}'
            AND sdrds.suggestedChannelId IN (4, 5)
            AND sdrdsd.messageUID LIKE 'sendNCO%'
            AND sdrds.isActive = 1;
        
        '''

        df = DataAccessLayer.read_rds_table_as_pandas(sql)
        
        data.set_dataframe("prediction_df", df)

    def execute(self):
        if Constants.LEARNING_MODE:
            self.read_training_data()
        if Constants.EXECUTION_MODE:
            self.read_prediction_data()
