{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f8150932-3b17-4134-bab0-31f8eb98a7c4", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "pd.set_option('display.max_columns', None)\n", "\n", "sns.set(style=\"whitegrid\")\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 2, "id": "b097d337-dad5-4ddc-8a17-fabc80671cfb", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>contentTopicName</th>\n", "      <th>cmsDocumentId</th>\n", "      <th>fragmentId</th>\n", "      <th>probability</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>59</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>1</td>\n", "      <td>0.035310</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>59</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>2</td>\n", "      <td>0.035310</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>59</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>3</td>\n", "      <td>0.035310</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>59</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>4</td>\n", "      <td>0.033901</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>92</td>\n", "      <td>CGM vs. BGM</td>\n", "      <td>ADC-94563</td>\n", "      <td>1</td>\n", "      <td>0.292987</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92572</th>\n", "      <td>634255</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>4</td>\n", "      <td>0.024113</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92573</th>\n", "      <td>634270</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>1</td>\n", "      <td>0.139802</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92574</th>\n", "      <td>634270</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>2</td>\n", "      <td>0.139802</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92575</th>\n", "      <td>634270</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>3</td>\n", "      <td>0.139802</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92576</th>\n", "      <td>634270</td>\n", "      <td>A1C is not enough/Value of Mgmt Tools</td>\n", "      <td>ADC-102760</td>\n", "      <td>4</td>\n", "      <td>0.146622</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>92577 rows × 5 columns</p>\n", "</div>"], "text/plain": ["       accountId                       contentTopicName cmsDocumentId  \\\n", "0             59  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "1             59  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "2             59  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "3             59  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "4             92                            CGM vs. BGM     ADC-94563   \n", "...          ...                                    ...           ...   \n", "92572     634255  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "92573     634270  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "92574     634270  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "92575     634270  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "92576     634270  A1C is not enough/Value of Mgmt Tools    ADC-102760   \n", "\n", "       fragmentId  probability  \n", "0               1     0.035310  \n", "1               2     0.035310  \n", "2               3     0.035310  \n", "3               4     0.033901  \n", "4               1     0.292987  \n", "...           ...          ...  \n", "92572           4     0.024113  \n", "92573           1     0.139802  \n", "92574           2     0.139802  \n", "92575           3     0.139802  \n", "92576           4     0.146622  \n", "\n", "[92577 rows x 5 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# df = pd.read_parquet(\"s3://aktana-bdp-abbottus/preprod/content_selection/predictions/runmonth=2025-08/results_2025-08-04.parquet\")\n", "df = pd.read_parquet(\"s3://aktana-bdp-abbottus/prod/content_selection/predictions/runmonth=2025-07/results_2025-07-24.parquet\")\n", "df"]}, {"cell_type": "code", "execution_count": 3, "id": "8fdd1cbb-05be-424d-b6fa-d3d2c4c51010", "metadata": {}, "outputs": [{"data": {"text/plain": ["22657"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df['accountId'].nunique()"]}, {"cell_type": "code", "execution_count": 4, "id": "1648cf3a-7081-4cd4-999f-1d33cff3fe37", "metadata": {}, "outputs": [{"data": {"text/plain": ["cmsDocumentId\n", "ADC-102760    16062\n", "ADC-102761        3\n", "ADC-94563      1405\n", "ADC-94575      4499\n", "ADC-94590       144\n", "ADC-96117       544\n", "Name: accountId, dtype: int64"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df.groupby('cmsDocumentId')['accountId'].nunique()"]}, {"cell_type": "markdown", "id": "9cce3b3b-7ba0-440b-9cb3-94cd544e64c2", "metadata": {}, "source": ["# Max Probability per (accountId, cmsDocumentId) and its Distribution"]}, {"cell_type": "code", "execution_count": 5, "id": "eb3a37b8-4f20-4cff-b72a-0ba7e0c0e4ea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   accountId cmsDocumentId  max_probability\n", "0         59    ADC-102760         0.035310\n", "1         92     ADC-94563         0.321760\n", "2        125     ADC-94575         0.053361\n", "3        133    ADC-102760         0.175619\n", "4        150     ADC-94575         0.077659\n", "5        193     ADC-94575         0.199455\n", "6        267     ADC-94575         0.090203\n", "7        328     ADC-94563         0.003456\n", "8        334     ADC-94563         0.598743\n", "9        371    ADC-102760         0.187887\n"]}], "source": ["max_prob_df = df.groupby(['accountId', 'cmsDocumentId'])['probability'].max().reset_index()\n", "max_prob_df.rename(columns={'probability': 'max_probability'}, inplace=True)\n", "\n", "print(max_prob_df.head(10))"]}, {"cell_type": "code", "execution_count": 6, "id": "8527b8f4-9eb6-4eda-8e07-404facaed3dc", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "sns.histplot(max_prob_df['max_probability'], bins=20)\n", "plt.title('Distribution of Max Probability per (Account, CMS Document)')\n", "plt.xlabel('Max Probability')\n", "plt.ylabel('Frequency')\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "be3ae881-44ba-44a0-968f-9fd112035c2f", "metadata": {}, "source": ["# Difference Across Fragments per (accountId, cmsDocumentId)"]}, {"cell_type": "code", "execution_count": 7, "id": "bdba7fa9-eec8-4a98-b376-10bcef940b4b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   accountId cmsDocumentId       max       min  probability_range\n", "0        334     ADC-94563  0.598743  0.393992           0.204752\n", "1        452     ADC-94575  0.164371  0.101497           0.062874\n", "2       1390     ADC-94575  0.794551  0.701232           0.093319\n", "3       1393     ADC-94575  0.460968  0.356635           0.104333\n", "4       1506     ADC-94575  0.579588  0.500319           0.079268\n", "5       1534     ADC-94575  0.505400  0.450308           0.055092\n", "6       1645     ADC-94575  0.210262  0.160163           0.050099\n", "7       2000     ADC-94563  0.455946  0.387851           0.068095\n", "8       2029    ADC-102760  0.156952  0.104631           0.052321\n", "9       2498     ADC-94575  0.889945  0.823402           0.066543\n"]}], "source": ["# Group by accountId and cmsDocumentId\n", "agg_diff_df = df.groupby(['accountId', 'cmsDocumentId'])['probability'].agg(['max', 'min'])\n", "agg_diff_df['probability_range'] = agg_diff_df['max'] - agg_diff_df['min']\n", "agg_diff_df = agg_diff_df[agg_diff_df.probability_range > 0.05]\n", "agg_diff_df = agg_diff_df.reset_index()\n", "\n", "print(agg_diff_df.head(10))"]}, {"cell_type": "code", "execution_count": 8, "id": "0f048986-c0ce-49a8-b3c6-401be4cbb070", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "sns.histplot(agg_diff_df['probability_range'], bins=10)\n", "plt.title('Distribution of Probability Range Across Fragments')\n", "plt.xlabel('Probability Range (Max - Min)')\n", "plt.ylabel('Frequency')\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "690c93a6-3a52-40fb-b228-3eab9cee58dc", "metadata": {}, "source": ["## Distribution of Probability Range by Document"]}, {"cell_type": "code", "execution_count": 9, "id": "9910e1b1-64a1-4a74-aeae-a70ada318f3c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "agg_diff_df = df.groupby(['accountId', 'cmsDocumentId'])['probability'].agg(['max', 'min'])\n", "agg_diff_df['probability_range'] = agg_diff_df['max'] - agg_diff_df['min']\n", "agg_diff_df = agg_diff_df[agg_diff_df.probability_range > 0.05]\n", "agg_diff_df = agg_diff_df.reset_index()\n", "\n", "unique_docs = agg_diff_df['cmsDocumentId'].unique()\n", "\n", "for doc_id in unique_docs:\n", "    doc_df = agg_diff_df[agg_diff_df['cmsDocumentId'] == doc_id]\n", "\n", "    if doc_df.empty:\n", "        continue  # Skip if no data for this document\n", "\n", "    plt.figure(figsize=(10, 6))\n", "    sns.histplot(doc_df['probability_range'], bins=10)\n", "    plt.title(f'Distribution of Probability Range for cmsDocumentId: {doc_id}')\n", "    plt.xlabel('Probability Range (Max - Min)')\n", "    plt.ylabel('Frequency')\n", "    plt.tight_layout()\n", "    plt.show()\n"]}, {"cell_type": "markdown", "id": "873af77c-0a82-4e1d-b4b7-577247dee87a", "metadata": {}, "source": ["# Differences Across Accounts for same (cmsDocumentId, fragmentId)"]}, {"cell_type": "code", "execution_count": 10, "id": "62b7821c-17e3-46cf-8de6-49532401dbfb", "metadata": {}, "outputs": [], "source": ["# x-axis: # of accts with specific frag being winner\n", "# y-axis: doc"]}, {"cell_type": "code", "execution_count": 11, "id": "b51a0314-01a0-4dcd-a3fd-d06fcea5e6d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   cmsDocumentId  fragmentId  num_accounts\n", "0     ADC-102760           1          6078\n", "1     ADC-102760           2          6078\n", "2     ADC-102760           3          6078\n", "3     ADC-102760           4          9984\n", "4     ADC-102761           4             3\n", "5      ADC-94563           1           293\n", "6      ADC-94563           2           293\n", "7      ADC-94563           3           293\n", "8      ADC-94563           4           908\n", "9      ADC-94563           5           204\n", "10     ADC-94575           1          2958\n", "11     ADC-94575           2          2958\n", "12     ADC-94575           3          1541\n", "13     ADC-94575           4          1541\n", "14     ADC-94590           1            75\n", "15     ADC-94590           2            75\n", "16     ADC-94590           3            69\n", "17     ADC-94590           4            69\n", "18     ADC-96117           1           544\n", "19     ADC-96117           2           544\n", "20     ADC-96117           3           544\n", "21     ADC-96117           4           544\n", "22     ADC-96117           5           544\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "df['max_prob'] = df.groupby(['accountId', 'cmsDocumentId'])['probability'].transform('max')\n", "winners = df[df['probability'] == df['max_prob']].copy()\n", "\n", "winner_counts = winners.groupby(['cmsDocumentId', 'fragmentId'])['accountId'].nunique().reset_index()\n", "winner_counts.rename(columns={'accountId': 'num_accounts'}, inplace=True)\n", "\n", "print(winner_counts)\n", "\n", "plt.figure(figsize=(12, 6))\n", "sns.swarmplot(\n", "    data=winner_counts,\n", "    x='num_accounts',\n", "    y='cmsDocumentId',\n", "    hue='fragmentId',\n", "    dodge=True,\n", "    palette='deep'\n", ")\n", "\n", "plt.title('Number of Accounts per Document with Fragment as Winner')\n", "plt.xlabel('Number of Accounts')\n", "plt.ylabel('CMS Document ID')\n", "plt.legend(title='Fragment ID', bbox_to_anchor=(1.05, 1), loc='upper left')\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 14, "id": "bdb1e429-034f-4b22-bf27-a35908d182f9", "metadata": {}, "outputs": [], "source": ["x_percent_threshold = 0.05\n", "\n", "def find_confident_winners(group):\n", "    sorted_group = group.sort_values('probability', ascending=False)\n", "    top_probs = sorted_group['probability'].unique()\n", "\n", "    max_prob = top_probs[0]\n", "    second_best_prob = top_probs[1]\n", "\n", "    if (max_prob - second_best_prob) >= x_percent_threshold:\n", "        return sorted_group[sorted_group['probability'] == max_prob]\n", "    else:\n", "        return pd.DataFrame(columns=group.columns)\n", "\n", "confident_winners = (\n", "    df.groupby(['accountId', 'cmsDocumentId'], group_keys=False)\n", "      .apply(find_confident_winners)\n", "      .reset_index(drop=True)\n", ")\n"]}, {"cell_type": "code", "execution_count": 16, "id": "375dc737-7e2c-46c4-ba4e-03f755292678", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   cmsDocumentId  fragmentId  num_accounts\n", "0     ADC-102760           1          1262\n", "1     ADC-102760           2          1262\n", "2     ADC-102760           3          1262\n", "3     ADC-102760           4          1273\n", "4      ADC-94563           1            21\n", "5      ADC-94563           2            21\n", "6      ADC-94563           3            21\n", "7      ADC-94563           4           130\n", "8      ADC-94563           5             1\n", "9      ADC-94575           1           972\n", "10     ADC-94575           2           972\n", "11     ADC-94575           3           198\n", "12     ADC-94575           4           198\n", "13     ADC-94590           1            10\n", "14     ADC-94590           2            10\n", "15     ADC-94590           3             7\n", "16     ADC-94590           4             7\n", "17     ADC-96117           1           544\n", "18     ADC-96117           2           544\n", "19     ADC-96117           3           544\n", "20     ADC-96117           4           544\n", "21     ADC-96117           5           544\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["confident_winner_counts = confident_winners.groupby(['cmsDocumentId', 'fragmentId'])['accountId'].nunique().reset_index()\n", "confident_winner_counts.rename(columns={'accountId': 'num_accounts'}, inplace=True)\n", "\n", "print(confident_winner_counts)\n", "\n", "plt.figure(figsize=(12, 6))\n", "sns.swarmplot(\n", "    data=confident_winner_counts,\n", "    x='num_accounts',\n", "    y='cmsDocumentId',\n", "    hue='fragmentId',\n", "    dodge=True,\n", "    palette='deep'\n", ")\n", "\n", "plt.title('Number of Accounts per Document with Fragment as Winner')\n", "plt.xlabel('Number of Accounts')\n", "plt.ylabel('CMS Document ID')\n", "plt.legend(title='Fragment ID', bbox_to_anchor=(1.05, 1), loc='upper left')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 27, "id": "42e6db7d-0b53-4fb1-ba03-e15ff313d570", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cmsDocumentId</th>\n", "      <th>distinct_accounts</th>\n", "      <th>accounts_with_clear_winner</th>\n", "      <th>ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ADC-102760</td>\n", "      <td>16062</td>\n", "      <td>2535</td>\n", "      <td>0.157826</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ADC-102761</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ADC-94563</td>\n", "      <td>1405</td>\n", "      <td>152</td>\n", "      <td>0.108185</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ADC-94575</td>\n", "      <td>4499</td>\n", "      <td>1170</td>\n", "      <td>0.260058</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ADC-94590</td>\n", "      <td>144</td>\n", "      <td>17</td>\n", "      <td>0.118056</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>ADC-96117</td>\n", "      <td>544</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  cmsDocumentId  distinct_accounts  accounts_with_clear_winner     ratio\n", "0    ADC-102760              16062                        2535  0.157826\n", "1    ADC-102761                  3                           0  0.000000\n", "2     ADC-94563               1405                         152  0.108185\n", "3     ADC-94575               4499                        1170  0.260058\n", "4     ADC-94590                144                          17  0.118056\n", "5     ADC-96117                544                           0  0.000000"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["threshold = 0.05\n", "\n", "def get_top2_distinct(probs):\n", "    unique_sorted = sorted(set(probs), reverse=True)\n", "    if len(unique_sorted) >= 2:\n", "        return [unique_sorted[0], unique_sorted[1]]\n", "    elif len(unique_sorted) == 1:\n", "        return [unique_sorted[0], None]\n", "    else:\n", "        return [None, None]\n", "\n", "\n", "top_probs = (\n", "    df\n", "    .groupby(['cmsDocumentId', 'accountId'])['probability']\n", "    .agg(get_top2_distinct)\n", "    .reset_index()\n", ")\n", "\n", "\n", "top_probs[['top1', 'top2']] = pd.DataFrame(top_probs['probability'].tolist(), index=top_probs.index)\n", "top_probs.drop(columns='probability', inplace=True)\n", "\n", "top_probs['clear_winner'] = (\n", "    top_probs['top2'].notna() & \n", "    ((top_probs['top1'] - top_probs['top2']).abs() >= threshold)\n", ")\n", "\n", "summary = (\n", "    top_probs\n", "    .groupby('cmsDocumentId')\n", "    .agg(\n", "        distinct_accounts=('accountId', 'nunique'),\n", "        accounts_with_clear_winner=('clear_winner', 'sum')\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "summary['ratio'] =  summary['accounts_with_clear_winner'] / summary['distinct_accounts']\n", "summary\n"]}, {"cell_type": "markdown", "id": "e4fded3e-0f77-40da-91a9-469811b949cf", "metadata": {}, "source": ["# examples"]}, {"cell_type": "code", "execution_count": 18, "id": "cd8382f8-4dc8-4b97-a5cf-b682aea5d411", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>cmsDocumentId</th>\n", "      <th>fragmentId</th>\n", "      <th>probability</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>16076</th>\n", "      <td>94770</td>\n", "      <td>ADC-102760</td>\n", "      <td>1</td>\n", "      <td>0.203264</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16077</th>\n", "      <td>94770</td>\n", "      <td>ADC-102760</td>\n", "      <td>2</td>\n", "      <td>0.203264</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16078</th>\n", "      <td>94770</td>\n", "      <td>ADC-102760</td>\n", "      <td>3</td>\n", "      <td>0.203264</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16079</th>\n", "      <td>94770</td>\n", "      <td>ADC-102760</td>\n", "      <td>4</td>\n", "      <td>0.310509</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16535</th>\n", "      <td>96812</td>\n", "      <td>ADC-94563</td>\n", "      <td>1</td>\n", "      <td>0.374848</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16536</th>\n", "      <td>96812</td>\n", "      <td>ADC-94563</td>\n", "      <td>2</td>\n", "      <td>0.374848</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16537</th>\n", "      <td>96812</td>\n", "      <td>ADC-94563</td>\n", "      <td>3</td>\n", "      <td>0.374848</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16538</th>\n", "      <td>96812</td>\n", "      <td>ADC-94563</td>\n", "      <td>4</td>\n", "      <td>0.488083</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16539</th>\n", "      <td>96812</td>\n", "      <td>ADC-94563</td>\n", "      <td>5</td>\n", "      <td>0.412752</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18258</th>\n", "      <td>103546</td>\n", "      <td>ADC-102760</td>\n", "      <td>1</td>\n", "      <td>0.712739</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18259</th>\n", "      <td>103546</td>\n", "      <td>ADC-102760</td>\n", "      <td>2</td>\n", "      <td>0.712739</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18260</th>\n", "      <td>103546</td>\n", "      <td>ADC-102760</td>\n", "      <td>3</td>\n", "      <td>0.712739</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18261</th>\n", "      <td>103546</td>\n", "      <td>ADC-102760</td>\n", "      <td>4</td>\n", "      <td>0.279723</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31148</th>\n", "      <td>154833</td>\n", "      <td>ADC-102760</td>\n", "      <td>1</td>\n", "      <td>0.228971</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31149</th>\n", "      <td>154833</td>\n", "      <td>ADC-102760</td>\n", "      <td>2</td>\n", "      <td>0.228971</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31150</th>\n", "      <td>154833</td>\n", "      <td>ADC-102760</td>\n", "      <td>3</td>\n", "      <td>0.228971</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31151</th>\n", "      <td>154833</td>\n", "      <td>ADC-102760</td>\n", "      <td>4</td>\n", "      <td>0.354403</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41850</th>\n", "      <td>198008</td>\n", "      <td>ADC-102760</td>\n", "      <td>1</td>\n", "      <td>0.622158</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41851</th>\n", "      <td>198008</td>\n", "      <td>ADC-102760</td>\n", "      <td>2</td>\n", "      <td>0.622158</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41852</th>\n", "      <td>198008</td>\n", "      <td>ADC-102760</td>\n", "      <td>3</td>\n", "      <td>0.622158</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41853</th>\n", "      <td>198008</td>\n", "      <td>ADC-102760</td>\n", "      <td>4</td>\n", "      <td>0.473377</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46679</th>\n", "      <td>218259</td>\n", "      <td>ADC-94575</td>\n", "      <td>1</td>\n", "      <td>0.267029</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46680</th>\n", "      <td>218259</td>\n", "      <td>ADC-94575</td>\n", "      <td>2</td>\n", "      <td>0.267029</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46681</th>\n", "      <td>218259</td>\n", "      <td>ADC-94575</td>\n", "      <td>3</td>\n", "      <td>0.133962</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46682</th>\n", "      <td>218259</td>\n", "      <td>ADC-94575</td>\n", "      <td>4</td>\n", "      <td>0.133962</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48417</th>\n", "      <td>223979</td>\n", "      <td>ADC-94575</td>\n", "      <td>1</td>\n", "      <td>0.418049</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48418</th>\n", "      <td>223979</td>\n", "      <td>ADC-94575</td>\n", "      <td>2</td>\n", "      <td>0.418049</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48419</th>\n", "      <td>223979</td>\n", "      <td>ADC-94575</td>\n", "      <td>3</td>\n", "      <td>0.274259</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48420</th>\n", "      <td>223979</td>\n", "      <td>ADC-94575</td>\n", "      <td>4</td>\n", "      <td>0.274259</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51017</th>\n", "      <td>234689</td>\n", "      <td>ADC-94575</td>\n", "      <td>1</td>\n", "      <td>0.190186</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51018</th>\n", "      <td>234689</td>\n", "      <td>ADC-94575</td>\n", "      <td>2</td>\n", "      <td>0.190186</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51019</th>\n", "      <td>234689</td>\n", "      <td>ADC-94575</td>\n", "      <td>3</td>\n", "      <td>0.321920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51020</th>\n", "      <td>234689</td>\n", "      <td>ADC-94575</td>\n", "      <td>4</td>\n", "      <td>0.321920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62193</th>\n", "      <td>300304</td>\n", "      <td>ADC-102760</td>\n", "      <td>1</td>\n", "      <td>0.243084</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62194</th>\n", "      <td>300304</td>\n", "      <td>ADC-102760</td>\n", "      <td>2</td>\n", "      <td>0.243084</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62195</th>\n", "      <td>300304</td>\n", "      <td>ADC-102760</td>\n", "      <td>3</td>\n", "      <td>0.243084</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62196</th>\n", "      <td>300304</td>\n", "      <td>ADC-102760</td>\n", "      <td>4</td>\n", "      <td>0.129491</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87198</th>\n", "      <td>574657</td>\n", "      <td>ADC-102760</td>\n", "      <td>1</td>\n", "      <td>0.516333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87199</th>\n", "      <td>574657</td>\n", "      <td>ADC-102760</td>\n", "      <td>2</td>\n", "      <td>0.516333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87200</th>\n", "      <td>574657</td>\n", "      <td>ADC-102760</td>\n", "      <td>3</td>\n", "      <td>0.516333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87201</th>\n", "      <td>574657</td>\n", "      <td>ADC-102760</td>\n", "      <td>4</td>\n", "      <td>0.619800</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       accountId cmsDocumentId  fragmentId  probability\n", "16076      94770    ADC-102760           1     0.203264\n", "16077      94770    ADC-102760           2     0.203264\n", "16078      94770    ADC-102760           3     0.203264\n", "16079      94770    ADC-102760           4     0.310509\n", "16535      96812     ADC-94563           1     0.374848\n", "16536      96812     ADC-94563           2     0.374848\n", "16537      96812     ADC-94563           3     0.374848\n", "16538      96812     ADC-94563           4     0.488083\n", "16539      96812     ADC-94563           5     0.412752\n", "18258     103546    ADC-102760           1     0.712739\n", "18259     103546    ADC-102760           2     0.712739\n", "18260     103546    ADC-102760           3     0.712739\n", "18261     103546    ADC-102760           4     0.279723\n", "31148     154833    ADC-102760           1     0.228971\n", "31149     154833    ADC-102760           2     0.228971\n", "31150     154833    ADC-102760           3     0.228971\n", "31151     154833    ADC-102760           4     0.354403\n", "41850     198008    ADC-102760           1     0.622158\n", "41851     198008    ADC-102760           2     0.622158\n", "41852     198008    ADC-102760           3     0.622158\n", "41853     198008    ADC-102760           4     0.473377\n", "46679     218259     ADC-94575           1     0.267029\n", "46680     218259     ADC-94575           2     0.267029\n", "46681     218259     ADC-94575           3     0.133962\n", "46682     218259     ADC-94575           4     0.133962\n", "48417     223979     ADC-94575           1     0.418049\n", "48418     223979     ADC-94575           2     0.418049\n", "48419     223979     ADC-94575           3     0.274259\n", "48420     223979     ADC-94575           4     0.274259\n", "51017     234689     ADC-94575           1     0.190186\n", "51018     234689     ADC-94575           2     0.190186\n", "51019     234689     ADC-94575           3     0.321920\n", "51020     234689     ADC-94575           4     0.321920\n", "62193     300304    ADC-102760           1     0.243084\n", "62194     300304    ADC-102760           2     0.243084\n", "62195     300304    ADC-102760           3     0.243084\n", "62196     300304    ADC-102760           4     0.129491\n", "87198     574657    ADC-102760           1     0.516333\n", "87199     574657    ADC-102760           2     0.516333\n", "87200     574657    ADC-102760           3     0.516333\n", "87201     574657    ADC-102760           4     0.619800"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "\n", "valid_accounts = agg_diff_df[agg_diff_df.probability_range > 0.1]\n", "sample_account = np.random.choice(valid_accounts.accountId, 10)\n", "\n", "account_df = df[df['accountId'].isin(sample_account)].copy()\n", "\n", "\n", "account_df = account_df.sort_values(by=['accountId', 'cmsDocumentId', 'fragmentId'])\n", "account_df[['accountId', 'cmsDocumentId', 'fragmentId', 'probability']]"]}, {"cell_type": "code", "execution_count": null, "id": "2a8c429f-cef0-40d2-9c18-ce83171a50bd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}